import cv2
import numpy as np


def get_valid_depth(depth_frame, u, v):
    """Get valid depth value from a depth frame at given pixel coordinates.

    Args:
        depth_frame (np.ndarray): Depth image from depth camera with depth values in (meters)
        u (int): Horizontal pixel coordinate
        v (int): Vertical pixel coordinate

    Returns:
        float: Valid depth value in meters. Returns 0 if no valid depth found.
    """
    h, w = depth_frame.shape
    
    for window in [1, 3, 5]:
        half_win = window // 2
        # Extract patch and remove invalid values
        patch = depth_frame[
            max(0, v - half_win):min(h, v + half_win + 1),
            max(0, u - half_win):min(w, u + half_win + 1)
        ]
        valid_values = patch[np.isfinite(patch) & (patch > 0)]
        
        if valid_values.size > 0:
            return float(np.nanmedian(valid_values))
    return 0    # fallback if nothing found


def get_face_landmarks_in_ccs_from_depth(
    face_landmarks_2D: np.ndarray,
    depth_frame: np.ndarray,
    camera_matrix: np.ndarray,
    filter_depth: bool = True,
    depth_range: tuple = (0.1, 2.0),
    depth_mm2m: float = 1000.0,
) -> np.ndarray:
    """
    Convert 2D facial landmarks to 3D camera coordinate system using depth information.

    This function takes 2D pixel coordinates of facial landmarks and uses the corresponding
    depth values to compute their 3D positions in the camera coordinate system.

    Args:
        face_landmarks_2D: Array of 2D facial landmarks with shape (N, 2) containing [u, v] pixel coordinates
        depth_frame: Depth image from depth camera (height x width array) with depth values in millimeters
        camera_matrix: 3x3 intrinsic camera matrix containing focal lengths and principal point
        filter_depth: Whether to filter out depth values outside the specified range
        depth_range: Tuple (min, max) specifying the valid depth range in meters
        depth_mm2m: Conversion factor from millimeters to meters

    Returns:
        3D facial landmarks in camera coordinate system with shape (3, N) containing [X, Y, Z] coordinates
    """
    height, width = depth_frame.shape
    
    fx = camera_matrix[0, 0]  # Focal length in x
    fy = camera_matrix[1, 1]  # Focal length in y
    cx = camera_matrix[0, 2]  # Principal point x
    cy = camera_matrix[1, 2]  # Principal point y

    landmarks_3D = []
    for (u, v) in face_landmarks_2D:
        # Ensure integer pixel coordinates and valid depth retrieval
        u, v = int(round(u)), int(round(v))

        # Out-of-bounds handling
        if not ((0 <= u < width) and (0 <= v < height)):
            landmarks_3D.append([np.nan, np.nan, np.nan])
            continue

        # Get the depth at the landmark pixel (millimeters)
        if filter_depth:
            d = get_valid_depth(depth_frame, u, v)
        else:
            d = depth_frame[v, u]

        # If depth is invalid or unreasonably near/far, skip
        if (d == 0) or not (depth_range[0] <= d/depth_mm2m <= depth_range[1]):
            landmarks_3D.append([np.nan, np.nan, np.nan])
            continue

        # Convert from pixel coordinates (u, v) plus depth d to 3D coordinates
        X = (u - cx) * d / fx
        Y = (v - cy) * d / fy
        Z = d
        landmarks_3D.append([X, Y, Z])

    return np.array(landmarks_3D)  # Shape: (3, N)


def project_point(point3d, K):
    X, Y, Z = point3d
    u = (K[0, 0] * X + K[0, 2] * Z) / Z
    v = (K[1, 1] * Y + K[1, 2] * Z) / Z
    return np.array([u, v], dtype=float)


def draw_coordinate_axes_px(img, rvec, tvec, K, axis_length=80, thickness=3):
    """
    Draws a 3D coordinate system (axes) on the face in the image.
    :param img: Image to draw on
    :param face_center_3d: Origin point in 3D (camera space)
    :param R: 3x3 rotation matrix of the head
    :param K: Camera intrinsic matrix
    :param axis_length: Length of axes to draw (in mm)
    """
    # Axes in 3D camera space
    axes = np.eye(3) * axis_length  # X, Y, Z axes

    R = cv2.Rodrigues(rvec)[0]
    
    # Rotate axes using head rotation matrix
    rotated_axes = R @ axes  # shape (3, 3)

    # Get endpoints in 3D
    endpoints_3d = tvec.reshape(3, 1) + rotated_axes

    # Project to 2D
    origin_2d = project_point(tvec, K)
    x_2d = project_point(endpoints_3d[:, 0], K)
    y_2d = project_point(endpoints_3d[:, 1], K)
    z_2d = project_point(endpoints_3d[:, 2], K)

    # Convert to int tuples
    origin_2d = tuple(map(int, origin_2d))
    x_2d = tuple(map(int, x_2d))
    y_2d = tuple(map(int, y_2d))
    z_2d = tuple(map(int, z_2d))

    # Draw each axis: X (Red), Y (Green), Z (White)
    cv2.line(img, origin_2d, z_2d, (255, 255, 255), thickness) # Z: directed inside head
    cv2.line(img, origin_2d, x_2d, (0, 0, 0), thickness)
    cv2.line(img, origin_2d, y_2d, (0, 0, 0), thickness)

    return img


def compute_rvec_from_landmarks(landmarks_3D):
    """
    Compute head rotation vector (rvec) from 3D facial landmarks in camera coordinate system.

    :param landmarks_3D: np.ndarray of shape (3, N), 3D facial landmarks in camera space
    :return: rvec (3x1 rotation vector), rotation_matrix (3x3)
    """
    # Define landmark indices for key facial features
    right_eye_indices = [1, 2]      # Subject's right eye (from camera view: left)
    left_eye_indices = [3, 4]      # Subject's left eye (from camera view: right)
    mouth_indices = [5, 6]          # Mouth corners

    # Compute key points by averaging landmark positions
    right_eye = np.mean(landmarks_3D[right_eye_indices], axis=0)
    left_eye = np.mean(landmarks_3D[left_eye_indices], axis=0)
    mouth_center = np.mean(landmarks_3D[mouth_indices], axis=0)
    eye_center = 0.5 * (left_eye + right_eye)

    # X-axis: from right eye to left eye (horizontal axis)
    x_axis = left_eye - right_eye
    x_axis /= np.linalg.norm(x_axis)

    # Y-axis: from eye center to mouth center (vertical axis)
    y_axis = mouth_center - eye_center
    y_axis /= np.linalg.norm(y_axis)

    # Z-axis: perpendicular to both X and Y (forward direction)
    z_axis = np.cross(x_axis, y_axis)
    z_axis /= np.linalg.norm(z_axis)

    # Re-orthogonalize Y-axis to ensure strict orthogonality
    y_axis = np.cross(z_axis, x_axis)
    y_axis /= np.linalg.norm(y_axis)

    # Rotation matrix: columns are the axes of face in camera space
    R = np.stack([x_axis, y_axis, z_axis], axis=1)  # Shape (3, 3)

    # Convert rotation matrix to rotation vector (Rodrigues)
    rvec, _ = cv2.Rodrigues(R)

    return rvec, R