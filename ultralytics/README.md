# Multi-Camera Pose Estimation System

A robust computer vision system for estimating and transforming human head poses across multiple calibrated cameras using intelligent origin selection.

## Overview

This system automatically selects the camera with the most visible facial keypoints as the origin for pose estimation, then transforms the estimated pose to all other camera viewpoints using extrinsic calibration data. This approach ensures maximum accuracy by using the best available data for each scene.

## Key Features

### 🎯 **Intelligent Origin Selection**
- Automatically finds the camera with the most visible keypoints for each scene
- Uses this optimal camera as the reference for pose estimation
- Handles varying keypoint visibility across different camera angles

### 🔄 **Multi-Camera Pose Transformation**
- Transforms poses from origin camera to target cameras using extrinsic matrices
- Supports both fisheye and pinhole camera models
- Maintains pose accuracy across different camera coordinate systems

### 📷 **Camera Support**
- **Fisheye cameras**: OMS cameras (cam_0, cam_2, cam_4, cam_6)
- **Pinhole cameras**: RealSense cameras (rs_146222252273, rs_146222251797)
- **CCTV cameras**: Additional pinhole cameras
- Automatic detection and handling of camera distortion types

### 🎨 **Visualization & Output**
- Real-time visualization with coordinate axes and keypoint overlays
- Saves processed images with pose projections
- Exports pose data (rotation/translation vectors) in JSON format
- Camera information overlays showing origin and target details

## System Architecture

```
Input Data → Origin Selection → Pose Estimation → Pose Transformation → Output
    ↓              ↓                ↓                    ↓              ↓
Images &       Find camera     Solve PnP on        Transform to     Images &
Keypoints      with most       origin camera       target cameras   Pose Data
               visible pts
```

## Quick Start

### Create Conda Environment

```bash
conda env create -f requirements.yml    # can take a while... (5 minutes)
```

### Basic Usage
```bash
# Run with visualization
python transform_pose_between_cameras.py -i data/images -j data/jsons --show-frame

# Save results to disk
python transform_pose_between_cameras.py -i data/images -j data/jsons --save

# Run ultralytics to generate 3D body keypoints (z=visibility)
python ultralytics_body_pose.py -i data/images --save
```

### Input Requirements
- **Images**: Multi-camera synchronized captures
- **Keypoints**: MediaPipe face mesh annotations with visibility flags
- **Calibration**: Camera intrinsic/extrinsic parameters in YAML format
