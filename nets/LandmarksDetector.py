import cv2
import torch
import onnxruntime
import numpy as np

from collections import Counter, deque


def test_calculate_visibility_confidence(frame, leftEye, rightEye, rightEyeHull):
    cLeft = np.round(np.mean(leftEye, axis=0)).astype(np.int32)
    cRight = np.round(np.mean(rightEye, axis=0)).astype(np.int32)

    px = 16
    x, y = cLeft
    confidence = calculate_visibility_confidence(frame, x, y, patch_size=16)
    
    color_new = (0, 0, 255) if confidence >= 0.3 else (0, 255, 0)
    cv2.circle(frame, (cRight[0], cRight[1]), 2, (0, 0, 0), -1, cv2.LINE_AA)
    cv2.circle(frame, (x, y), 2, (255, 255, 255), -1, cv2.LINE_AA)
    cv2.rectangle(frame, (x-px, y-px), (x+px, y+px), (255, 255, 255), 1)
    cv2.putText(frame, f"{confidence:.3f}", (rightEyeHull[0][0][0], rightEyeHull[0][0][1]-40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color_new, 2)


def calculate_visibility_confidence(image, x, y, patch_size=16, thresh=122):
    """
    Calculates visibility confidence based on the intensity and contrast of a patch.
    image: Grayscale image.
    x, y: Landmark coordinates.
    patch_size: Size of the patch to analyze.
    """
    height, width, c = image.shape
    x, y = int(x), int(y)
    half_patch = patch_size // 2
    
    # Extract patch around the landmark
    if (x - half_patch >= 0 and x + half_patch < width and
        y - half_patch >= 0 and y + half_patch < height):
        patch = image[y-half_patch:y+half_patch, x-half_patch:x+half_patch]
        gray = cv2.cvtColor(patch, cv2.COLOR_RGB2GRAY)

        # Apply Gaussian Blurring
        gray = cv2.GaussianBlur(gray, ksize=(3, 3), sigmaX=0, borderType=cv2.BORDER_DEFAULT)
        _, gray = cv2.threshold(gray, thresh=thresh, maxval=255, type=cv2.THRESH_BINARY)
        
        # Compute confidence as a function of contrast and intensity
        confidence = np.std(gray) / 255     # Normalize contrast to [0, 1]
        cv2.imshow("gray", gray)
        return confidence
    else:
        return 0.0      # Low confidence if patch is out of bounds


class LandmarksDetectorONNX:
    def __init__(self, onnx_path=None, apply_all_optim=True, device="cpu", maxlen_buffer=1, eye_dist_thresh=1.875, min_dist_thresh=1, max_dist_thresh=10) -> None:
        self.dist = 0
        self.device = device
        self.onnx_path = onnx_path
        self.apply_all_optim = apply_all_optim

        # Normalization parameters (ImageNet)
        self.mean = np.array([123.675, 116.28, 103.53], np.float64).reshape(1, -1)
        self.std = np.array([58.395, 57.12, 57.375], np.float64).reshape(1, -1)

        # Buffer parameters
        self.maxlen = maxlen_buffer
        self.most_frequent_drowsiness = None
        self.drowsiness_deque = deque(maxlen=self.maxlen)

        # Thresholds parameters
        self.eye_dist_thresh = eye_dist_thresh
        self.min_dist_thresh = min_dist_thresh
        self.max_dist_thresh = max_dist_thresh

    def load_model(self):
        # Set the provider for ONNX inference
        providers = ['CUDAExecutionProvider'] if self.device == "cuda" else ['CPUExecutionProvider']

        # Set graph optimization level
        sess_options = onnxruntime.SessionOptions()
        if self.apply_all_optim:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
        else:
            sess_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_EXTENDED

        # Create an inference session with the 3D_Landmarks and BFM ONNX models
        self.session = onnxruntime.InferenceSession(self.onnx_path, sess_options=sess_options, providers=providers)

        # Get inputs, outputs information for inference
        self.input_size = self.session.get_inputs()[0].shape[-1]			        # [b, c, h, w] --> w
        self.input_shape = tuple(self.session.get_inputs()[0].shape[-2:][::-1])     # (w, h)
        self.input_name = self.session.get_inputs()[0].name                         # "input"
        self.output_names = [output.name for output in self.session.get_outputs()]  # "output"

        return self
    
    def eye_aspect_ratio(self, eye):
        """ Calculate the eye aspect ratio """
        A = np.linalg.norm(eye[1] - eye[5])      # vertical-left
        B = np.linalg.norm(eye[2] - eye[4])      # vertical-right
        C = np.linalg.norm(eye[0] - eye[3])      # horizontal
        ear = (A + B) / (2.0 * C) * 2.5 * 100.0
        return ear
    
    def eye_opening_distance(self, eye):
        """ Calculate the eye opening distance """
        A = np.linalg.norm(eye[1] - eye[5])      # vertical-left
        B = np.linalg.norm(eye[2] - eye[4])      # vertical-right
        dist = (A + B) / 2.0
        return dist

    def postprocess(self, landmark_2D_12, avg_dist=False):
        """ Assess eye landmarks to predict drowsiness """
        # Get left and right eyes landmarks
        leftEye = landmark_2D_12[6:]
        rightEye = landmark_2D_12[:6]
        
        # Calculate eye opening distance
        if avg_dist:
            leftEAR = self.eye_opening_distance(leftEye)
            rightEAR = self.eye_opening_distance(rightEye)
            self.dist = (leftEAR + rightEAR) / 2.0
        else:
            self.dist = self.eye_opening_distance(rightEye)
            # self.ear = self.eye_aspect_ratio(rightEye)
            # self.dist = self.eye_aspect_ratio(rightEye)
        # print(round(self.ear))
        
        if self.dist < self.min_dist_thresh:
            self.dist = 0
        
        # Making decision based on the eye aspect ratio threshold
        if self.dist < self.eye_dist_thresh:
            self.drowsiness_deque.append("Drowsy")
        else:
            self.drowsiness_deque.append("Awake")

        # Get the most frequent result
        if self.drowsiness_deque:
            most_frequent_drowsiness = Counter(self.drowsiness_deque).most_common(n=1)[0][0]
        else:
            most_frequent_drowsiness = False
        return most_frequent_drowsiness

    def visualize(self, frame, landmark_2D_12, bbox):
        # Get left and right eyes landmarks
        leftEye = landmark_2D_12[6:]
        rightEye = landmark_2D_12[:6]
        
        # Form convex hulls for both eyes
        leftEyeHull = cv2.convexHull(leftEye)
        rightEyeHull = cv2.convexHull(rightEye)

        # Round the form convex hulls for both eyes
        leftEyeHull = np.round(leftEyeHull).astype(np.int32)
        rightEyeHull = np.round(rightEyeHull).astype(np.int32)
        
        if self.most_frequent_drowsiness == "Drowsy":
            color = (0, 0, 255)
        else:
            color = (0, 255, 0)

        # TESTITNG TODO
        # ===============================================================
        # test_calculate_visibility_confidence(frame, leftEye, rightEye, rightEyeHull)
        # ===============================================================
        
        # Plot facial landmarks
        lmk = np.round(landmark_2D_12).astype(np.int32)
        for i, (x, y) in enumerate(lmk):
            color_eye = (0, 255, 0) if i < 6 else (0, 0, 255)
            cv2.circle(frame, (x, y), 2, color_eye, -1, cv2.LINE_AA)

        # Calculate openness percent ()
        openness = max(0, min(int((self.dist - self.min_dist_thresh) / (self.max_dist_thresh-self.min_dist_thresh) * 100), 100))

        # Eye openness Bars
        for i, Eye in enumerate([rightEye, leftEye]):
            shift_top = -7
            bar_width = 8
            bar_height = 30
            bar_gap = 25
            if i == 0:
                bar_gap = -bar_gap-bar_width    # Gap between the eye and the bar

            # Position and size of the bar
            eye_center = np.round(np.mean(Eye, axis=0)).astype(np.int32)
            
            # Calculate bar start and end points
            bar_x1 = eye_center[0] + bar_gap
            bar_y1 = eye_center[1] - (bar_height // 2) + shift_top
            bar_x2 = bar_x1 + bar_width
            bar_y2 = bar_y1 + bar_height

            # Draw the filled portion of the bar based on eye openness
            filled_height = int(openness / 100 * bar_height)
            filled_y1 = bar_y2 - filled_height
            cv2.rectangle(frame, (bar_x1, filled_y1), (bar_x2, bar_y2), (0, 255, 0), -1)
            
            # Draw bar outline
            cv2.rectangle(frame, (bar_x1, bar_y1), (bar_x2, bar_y2), (255, 255, 255), 1)
        
        # Draw eyes contours and drowsiness result
        cv2.putText(frame, f"{self.most_frequent_drowsiness}", (leftEyeHull[0][0][0], leftEyeHull[0][0][1]-60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

        # Draw original and preprocessed face bboxes
        cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 0, 255), 2)

    def test(self, sample, target, index=[0, 9]):
        """ Test ONNX Model Performance """
        output = self.session.run(self.output_names, {self.input_name: sample.numpy()})[0]
        
        output = output.reshape(-1, 2).astype(np.float32)
        target = target.reshape(-1, 2).cpu().numpy().astype(np.float32)
        norm = np.linalg.norm(target[index[0]] - target[index[1]])
        nme = np.mean(np.linalg.norm(output - target, axis=1)) / norm
        return nme
    
    def evaluate(self, image, target_resolution=96):
        """ Evaluate Drowsiness-Feature Accuracy """
        scale = image.shape[0] / self.input_size
        
        # Pre-process the input
        image = cv2.resize(image, (self.input_size, self.input_size))
        
        image = image.astype(np.float32)

        cv2.cvtColor(image, cv2.COLOR_BGR2RGB, image)   # inplace
        cv2.subtract(image, self.mean, image)           # inplace
        cv2.multiply(image, 1 / self.std, image)        # inplace

        image = image.transpose((2, 0, 1))[np.newaxis, ...]
        image = np.ascontiguousarray(image)

        # Get prediction
        output = self.session.run(self.output_names, {self.input_name: image})[0]

        # Convert to landmarks
        landmark_2D_12 = output.reshape(-1, 2).astype(np.float32)
        landmark_2D_12[:, 0] = landmark_2D_12[:, 0] * self.input_size
        landmark_2D_12[:, 1] = landmark_2D_12[:, 1] * self.input_size

        landmark_2D_12 = landmark_2D_12 * scale
        landmark_2D_12 /= target_resolution
        
        rightEye = landmark_2D_12[6:].mean(axis=0)
        leftEye = landmark_2D_12[:6].mean(axis=0)
        
        return landmark_2D_12, rightEye, leftEye
        # Assess eye landmarks to make decision
        self.most_frequent_drowsiness = self.postprocess(landmark_2D_12, avg_dist=False)
        return self.most_frequent_drowsiness

    def predict(self, frame, bbox, verbose=False):
        """ Demo: ONNX Inference Pipeline to detect facial landmarks """
        # Pre-process the input
        x_min = bbox[0]
        y_min = bbox[1]
        x_max = bbox[2]
        y_max = bbox[3]
        
        box_w = x_max - x_min
        box_h = y_max - y_min

        # Crop the face
        image = frame[y_min:y_max, x_min:x_max, :]
        image = cv2.resize(image, (self.input_size, self.input_size))
        image = image.astype(np.float32)

        cv2.cvtColor(image, cv2.COLOR_BGR2RGB, image)   # inplace
        cv2.subtract(image, self.mean, image)           # inplace
        cv2.multiply(image, 1 / self.std, image)        # inplace

        image = image.transpose((2, 0, 1))[np.newaxis, ...]
        image = np.ascontiguousarray(image)

        # Get prediction
        output = self.session.run(self.output_names, {self.input_name: image})[0]

        # Convert to landmarks
        landmark_2D_12 = output.reshape(-1, 2).astype(np.float32)
        landmark_2D_12[:, 0] = landmark_2D_12[:, 0] * box_w + x_min
        landmark_2D_12[:, 1] = landmark_2D_12[:, 1] * box_h + y_min

        # Assess eye landmarks to make decision
        self.most_frequent_drowsiness = self.postprocess(landmark_2D_12, avg_dist=False)

        # For testing visualizsation
        if verbose:
            self.visualize(frame, landmark_2D_12, bbox)
        return landmark_2D_12, self.most_frequent_drowsiness
