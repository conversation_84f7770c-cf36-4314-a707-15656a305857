# Gaze Data Preprocessing Pipeline

A comprehensive data preprocessing pipeline for gaze tracking datasets that prepares raw data for deep learning models. The pipeline processes face and eye images along with corresponding gaze labels (`pitch` and `yaw` angles) to create normalized training data. It includes tools for data analysis, visualization, and quality assessment of gaze direction distributions.

## Project Overview

This pipeline provides a complete workflow for:
1. Preprocessing raw gaze data (images, depth, landmarks)
2. Normalizing face and eye regions
3. Analyzing data distributions
4. Visualizing gaze directions

### Data Preprocessing

To preprocess raw data, use the preprocessing script:

The script requires:
- `data.csv` file with gaze labels and metadata
- Raw images in `.jpg` format
- Depth data in `.npy` format
- Face landmarks in `.npy` format
- Camera calibration parameters in a YAML file

## Project Structure
```
├── scripts/
│   ├── prepare_gaze_data.py          # Script for gaze data preprocessing
│   ├── show_data_distribution.py     # Script for data distribution analysis
│   └── show_gaze_vector.py           # Script for visualizing gaze directions
├── utils/
│   ├── gaze.py                       # Gaze related utility functions
│   ├── templates.py                  # 3D Face template from MediaPipe
│   └── util.py                       # General utility functions
│── yamls
│   ├── config.yaml                   # Main configuration file
│   ├── depth_camera_calibration.yaml # Camera calibration parameters
│   └── requirements.yml              # Conda environment requirements
├── run.py                            # Core pipeline script
└── README.md
```

## Installation

1. Clone the repository:
```bash
git clone --branch max-gaze-data-preprocessing-pipeline --single-branch https://github.com/DeltaX-AI-Lab/3D_Gaze_Ground_Truth.git
cd max-gaze-data-preprocessing-pipeline
```

2. Create a conda environment:
```bash
conda env create -f yamls/requirements.yml
```

## Run preprocessing:
```bash
python run.py --run-all
```

This executes the complete pipeline defined in `run.py`, which includes:
- `--prepare-gaze-data`: Normalizes face and eye images
- `--show-data-distribution`: Analyzes gaze angle distributions
- `--show-gaze-vector`: Visualizes gaze directions
- `--run-all`: Run all preprocessing steps

## Configuration File

Edit `yamls/config.yaml` to set input/output paths:

```yaml
# Camera parameters
camera:
  intrinsics_path: "yamls/depth_camera_calibration.yaml"  # Intrinsic parameters of the depth camera

# Data preprocessing parameters
data_preprocessing:
  dataset_path: "datasets/DeltaX/dataset_raw"             # Input path for raw data
  output_path: "datasets/DeltaX/dataset_normalized"       # Output path for normalized data

# Data distribution parameters
data_distribution:
  dataset_path: "datasets/DeltaX/dataset_normalized"      # Input path for normalized data
  output_path: "outputs/data_distribution_results"        # Output path for plots and statistics

# Gaze visualization parameters
gaze_directions_visualization:
  dataset_path: "datasets/DeltaX/dataset_normalized"      # Input path for normalized data (gaze ground truth)
  output_path: "outputs/gaze_visualization_results"       # Output path for plots and statistics
```

## Key Components

- `scripts/prepare_gaze_data.py`: Main preprocessing script
- `scripts/show_data_distribution.py`: Data analysis and visualization
- `scripts/show_gaze_vector.py`: Gaze direction visualization
- `utils/gaze.py`: Core gaze processing functions
- `utils/util.py`: Utility functions for image processing
- `run.py`: Complete pipeline execution

## Technical Details

The preprocessing pipeline:
1. Loads raw images, depth data, and metadata
2. Computes 3D gaze targets from screen coordinates
3. Transforms 2D facial landmarks to 3D using depth data
4. Computes head pose from 3D landmarks
5. Normalizes face and eye regions to canonical views
6. Transforms 3D gaze vectors to 2D angles (pitch and yaw)
7. Saves normalized images and creates labels CSV

## Notes

- All angles are stored in radians but displayed in degrees in visualizations
- The pipeline supports both train and test dataset splits
- Configuration parameters are centralized in `yamls/config.yaml`
