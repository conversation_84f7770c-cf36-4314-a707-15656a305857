import os
import pandas as pd

from glob import glob
from collections import defaultdict, Counter


# Root folders
DATASETS = ['train', 'test']
IGNORE_KEYWORDS = ['dot', 'fish', 'pt']
CSV_NAME = 'data.csv'


def collect_images(root):
    image_paths = sorted(
        glob(os.path.join(root, "**", "*.jpg"), recursive=True)
    )
    
    image_paths = [path for path in image_paths if len(path.split("_")) == 5]
    image_paths = [path.split('/')[-1] for path in image_paths]
    return image_paths


def collect_csv_paths(csv_path):
    """Collect all file_name entries from a CSV"""
    df = pd.read_csv(csv_path)
    csv_filenames = df['file_name'].tolist()
    csv_filenames = [path.split('/')[-1] for path in csv_filenames]

    # Count how many times each path appears
    counter = Counter(csv_filenames)

    # Find duplicates: values that appear more than once
    duplicates = {name: count for name, count in counter.items() if count > 1}

    # Get indices where each duplicate appears
    duplicate_indices = defaultdict(list)

    for idx, name in enumerate(csv_filenames):
        if name in duplicates:
            duplicate_indices[name].append(idx)

    # Print results
    print(f"🔍 Found {len(duplicates)} duplicated file names:")
    for name, count in duplicates.items():
        print(f"  - {name}: {count} times at indices {duplicate_indices[name]}")
    
    return csv_filenames


def compare(dataset):
    print(f"\n📁 Checking dataset: {dataset}")
    image_folder = os.path.join(dataset)
    csv_path = os.path.join(dataset, CSV_NAME)

    image_paths = collect_images(image_folder)
    csv_paths = collect_csv_paths(csv_path)

    images = set(image_paths)
    csv_entries = set(csv_paths)
    
    print(f"  - Found {len(image_paths)} images and {len(images)} uniq images.")
    print(f"  - Found {len(csv_paths)} csv_paths and {len(csv_entries)} uniq CSV .")
    
    assert len(images) == len(image_paths), "Image paths do not match."
    assert len(csv_entries) == len(csv_paths), "CSV entries do not match."

    # Compare sets
    extra_images = images - csv_entries
    missing_images = csv_entries - images

    if extra_images:
        print(f"🟥 Extra image files not listed in CSV ({len(extra_images)}):")
        for img in sorted(extra_images):
            print(f"  - {img}")

    if missing_images:
        print(f"🟧 Paths listed in CSV but image not found ({len(missing_images)}):")
        for img in sorted(missing_images):
            print(f"  - {img}")

    if not extra_images and not missing_images:
        print("✅ Everything matches perfectly.")


# Run check
for ds in DATASETS:
    compare(ds)
