# Gaze Data Preprocessing Pipeline Configuration

# Camera parameters
camera:
  intrinsics_path: "yamls/depth_camera_calibration.yaml"  # Intrinsic parameters of the depth camera

# Data preprocessing parameters
data_preprocessing:
  dataset_path: "datasets/DeltaX/dataset_raw_merged_v1-v2-v3-v4"         # Input path for raw data
  output_path:  "datasets/DeltaX/dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5_test"   # Output path for normalized data

# Data distribution parameters
data_distribution:
  dataset_path: "datasets/DeltaX/dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5_test"  # Input path for normalized data
  output_path:  "outputs/data_distribution_results_v1-v2-v3-v4_SCALE_FDN411-600_5_test"           # Output path for plots and statistics

# Gaze visualization parameters
gaze_directions_visualization:
  dataset_path: "datasets/DeltaX/dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5_test"  # Input path for normalized data (gaze ground truth)
  output_path:  "outputs/gaze_visualization_results_v1-v2-v3-v4_SCALE_FDN411-600_5_test"          # Output path for plots and statistics

# Gaze visualization parameters
multiview:
  intrinsics_path: "yamls/calibrated_cameras_data_intrinsics_extrinsics_tvs_final.yml"
  dataset_path: "datasets/DeltaX/multiview"  # Input path for normalized data (gaze ground truth)
  output_path:  "outputs/multiview"          # Output path for plots and statistics
