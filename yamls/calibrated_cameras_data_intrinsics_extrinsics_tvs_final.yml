%YAML:1.0
---
nb_tv: 3
nb_camera: 10
nb_channel: 14

cam_0: # O<PERSON> (origin) (id = 0) RGB
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 995.51672008933781, 0., 1276.8370627997094, 0., 995.49674530837444, 875.73212391935715, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23884222078854914, -0.025551154902660082, -0.092122444224713107, 0.029872825629992526 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [ 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1. ]

cam_1: # <PERSON><PERSON> (origin) (id = 1) IR
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 995.51672008933781, 0., 1276.8370627997094, 0., 995.49674530837444, 875.73212391935715, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23884222078854914, -0.025551154902660082, -0.092122444224713107, 0.029872825629992526 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [ 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1. ]

cam_2: # OMS Fisheye (id = 2) RGB
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 997.58761451955877, 0., 1273.3487941464975, 0., 997.69851379720944, 873.43119455098179, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23519236516747918, -0.01564934717545418, -0.10770814991769842, 0.036587766862332408 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.99853275919424145,   0.011859750596508161,   0.052836305053663608,   -37.968827755033431,
          0.035018874583783471,  0.60280980588168764,   -0.79711606203599206,    752.71308879594847,
         -0.04130384048512218,   0.79779676876299188,    0.60151002361765249,    424.67896522405198,
          0.,                    0.,                     0.,                       1.                 ]

cam_3: # OMS Fisheye (id = 3) IR
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 997.58761451955877, 0., 1273.3487941464975, 0., 997.69851379720944, 873.43119455098179, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23519236516747918, -0.01564934717545418, -0.10770814991769842, 0.036587766862332408 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.99853275919424145,   0.011859750596508161,   0.052836305053663608,   -37.968827755033431,
          0.035018874583783471,  0.60280980588168764,   -0.79711606203599206,    752.71308879594847,
         -0.04130384048512218,   0.79779676876299188,    0.60151002361765249,    424.67896522405198,
          0.,                    0.,                     0.,                       1.                 ]

cam_4: # OMS Fisheye (id = 4) RGB
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 998.9556111718166, 0., 1281.6096705625323, 0., 998.49054516790761, 880.75642198515504, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.2364947388065943, -0.023857315006902597, -0.094579550921974551, 0.030329272826895366 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.63605088390240583,  -0.35827097750165515,    0.68343337624597822, -930.6746883709352,
          0.30390397882019399,   0.93040643060525763,    0.20490545513880032,  -28.61028256895095,
         -0.70928248585751996,   0.077367826442212725,   0.70066580813350976,  163.68010069287905,
          0.,                    0.,                     0.,                     1.                ]

cam_5: # OMS Fisheye (id = 5) IR
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 998.9556111718166, 0., 1281.6096705625323, 0., 998.49054516790761, 880.75642198515504, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.2364947388065943, -0.023857315006902597, -0.094579550921974551, 0.030329272826895366 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.63605088390240583,  -0.35827097750165515,    0.68343337624597822, -930.6746883709352,
          0.30390397882019399,   0.93040643060525763,    0.20490545513880032,  -28.61028256895095,
         -0.70928248585751996,   0.077367826442212725,   0.70066580813350976,  163.68010069287905,
          0.,                    0.,                     0.,                     1.                ]

cam_6: # OMS Fisheye (id = 6) RGB
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 988.11166397896204, 0., 1282.903403732015, 0., 988.05518461945883, 885.71654923503434, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23765127039718825, -0.027528968510007565, -0.095072556170993736, 0.031899969570357248 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.54941870624673661,    0.33262095636842043,   -0.766487041384732,      980.66673849028575,
        -0.18678617476777323,    0.94302573827982117,    0.2753422994336705,    -101.60435897713236,
         0.81440162705006014,   -0.0081090274605268558,  0.58024497717072854,    267.36378559956455,
         0.,                     0.,                     0.,                       1.                 ]

cam_7: # OMS Fisheye (id = 7) IR
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 988.11166397896204, 0., 1282.903403732015, 0., 988.05518461945883, 885.71654923503434, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23765127039718825, -0.027528968510007565, -0.095072556170993736, 0.031899969570357248 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.54941870624673661,    0.33262095636842043,   -0.766487041384732,      980.66673849028575,
        -0.18678617476777323,    0.94302573827982117,    0.2753422994336705,    -101.60435897713236,
         0.81440162705006014,   -0.0081090274605268558,  0.58024497717072854,    267.36378559956455,
         0.,                     0.,                     0.,                       1.                 ]

cam_8: # Anker Pinhole
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1001.6491209548277, 0., 976.97579500562495, 0., 1002.7752138083686, 539.61129702327219, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.059047263426278383, -0.024620567753372138, -0.0072093767032458747, 0.00075840710330233403, 0.058591467863618678 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.9921932479165172,     0.057229091744162158,   0.11080338373448184,   -519.35306699789862,
         0.038045643358174647,   0.70722938455890072,   -0.70595972026589537,    805.68147522942104,
        -0.1187648424843613,     0.70466405376929564,    0.69953090247328453,    342.68501545261483,
         0.,                     0.,                     0.,                     1.                   ]

rs_146222252273: # RealSence (serial = 146222252273) looking to the table
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 645.33380126953102, 0., 653.04211425781295, 0., 644.45666503906295, 403.85299682617199, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.056624382734298699, 0.069789588451385498, 3.9113074308259996e-06, 0.00087557663209736304, -0.022849775850772899 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1280
   img_height: 800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.86519301094437107,   -0.30066139708661099,    -0.40130260167975579,   819.59698917139588,
        -0.13856086691871627,    0.62579213015984614,    -0.76758393416533677,   822.28221191083162,
         0.48191486797124999,    0.71971309153851681,     0.49977107348897565,   326.70515681417635,
         0.,                     0.,                      0.,                      1.                 ]

rs_146222251797: # RealSence (serial = 146222251797) looking to the door
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 645.87921142578102, 0., 651.13519287109398, 0., 645.03900146484398, 397.36730957031301, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.056801863014698001, 0.0684636235237122, -0.00044242519652470898, 4.961517333867e-06, -0.0226080249994993 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1280
   img_height: 800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.7941762687007522,     0.14190695728294736,    0.59088617322395187,    59.316726596621422,
         0.37980411002645703,    0.64313313684810292,   -0.66492751958008922,   806.57524934402841,
        -0.47437631922299972,    0.75249065360482381,    0.45686422928242448,   310.80387498423499,
         0.,                     0.,                     0.,                      1.                  ]

tof_240600110: # TFT – Helios (serial = 240600110)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 368.05125427246094, 0., 324.86814880371094, 0., 368.05125427246094, 241.05181884765625, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ -0.061244849604065385, -0.10712711033955256, 0.17144459510839152, -0.10552883821643241 ]
   distortion_type: 1
   camera_group: 0
   img_width: 640
   img_height: 480
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         0.78013247827315879,    0.26317057413840755,   -0.56756899602695055,    715.49598836378857,
        -0.097960913952911857,   0.94741386884010292,    0.304648355431206,     -104.93486311473332,
         0.61789722096873845,   -0.18206649894225213,    0.76488875941670176,    -70.923806824140812,
         0.,                     0.,                     0.,                       1.                 ]

cctv_net_1: # CCTV Pinhole (net-1: 169.254.0.3)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1390.1537518952414, 0., 972.44906180394787, 0., 1400.6892657075039, 554.695860247886, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41143196985045177, 0.24502508434849757, -4.3043597876293512e-05, -0.00022460504321067086, -0.092394239388181018 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.98189259164796916,   0.11300064894429411,    -0.15204536101112298,   740.80246285857584,
         -0.056154599324373229,  0.59292028550755138,     0.80330081290159505, -1587.3962481041194,
          0.18092429201617294,   0.79729316337957679,    -0.57583852961280202,  2171.3309967614246,
          0.,                    0.,                      0.,                      1.                 ]

cctv_net_2: # CCTV Pinhole (net-2: 169.254.0.2)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1391.1703121086068, 0., 913.84060050517348, 0., 1401.8702849445347, 530.09274285945958, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41176514203892389, 0.24101522308094739, 7.4994957863281606e-05, -9.3364968161397941e-06, -0.087169080293643744 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.98045885063996574,  -0.068771489197168217,   0.18431203019705847,    -821.92923534875649,
          0.10853351688215089,   0.59231921485227013,    0.7983598333019909,    -1581.466360642723,
         -0.16407595166553038,   0.80276299739736034,   -0.57328060502223199,    2148.3185148876923,
          0.,                    0.,                     0.,                        1.                ]

tv_1: # small TV on the left side (respect to cam_0 - origin)
   width_px: 1920
   height_px: 1080
   width_mm: 1440
   height_mm: 810
   square_size_mm: 90
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -6.57377589e-01,  1.65769007e-02,  7.53378996e-01,  1.83404219e+03,
          3.34930768e-01,  9.02007435e-01,  2.72404054e-01, -4.29750996e+02,
         -6.75037841e-01,  4.31402126e-01, -5.98511587e-01,  8.98411306e+02,
          0.0,             0.0,             0.0,             1.0             ]


tv_2: # big TV in the middle (respect to cam_0 - origin)
   width_px: 1920
   height_px: 1080
   width_mm: 1680
   height_mm: 945
   square_size_mm: 105
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -9.99795864e-01,  8.88427547e-03,  1.81466099e-02,  8.92549147e+02,
          1.57358361e-02,  9.05742104e-01,  4.23537041e-01, -8.95873354e+00,
         -1.26733289e-02,  4.23736134e-01, -9.05697011e-01, -1.36806273e+02,
          0.0,             0.0,             0.0,             1.0              ]

tv_3: # small TV on the right side (respect to cam_0 - origin)
   width_px: 1920
   height_px: 1080
   width_mm: 1440
   height_mm: 810
   square_size_mm: 90
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -7.47370810e-01, -8.15729246e-03, -6.64357081e-01, -7.90279553e+02,
         -2.87501008e-01,  9.05431899e-01,  3.12307936e-01,  1.25767984e+02,
          5.98982506e-01,  4.24413166e-01, -6.79038602e-01, -1.26524498e+02,
          0.0,             0.0,             0.0,             1.0              ]

tv_1_cctv_net_1: # small TV on the left side (respect to cctv_net_1)
   width_px: 1920
   height_px: 1080
   width_mm: 1440
   height_mm: 810
   square_size_mm: 90
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.504535,  -0.0111223,  -0.863319,   -679.622,
          0.4139,    -0.880646,   -0.230543,   -129.662,
         -0.757714,  -0.473645,    0.448921,   2860.650,
          0.0,        0.0,         0.0,           1.0   ]

tv_2_cctv_net_1: # big TV in the middle (respect to cctv_net_1)
   width_px: 1920
   height_px: 1080
   width_mm: 1680
   height_mm: 945
   square_size_mm: 105
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.978516,  -0.0170792,  -0.205464,    900.275,
          0.113752,  -0.875879,   -0.468932,   -330.648,
         -0.171953,  -0.482229,    0.859004,   3249.900,
          0.0,        0.0,         0.0,           1.0   ]

tv_3_cctv_net_1: # small TV on the right side (respect to cctv_net_1)
   width_px: 1920
   height_px: 1080
   width_mm: 1440
   height_mm: 810
   square_size_mm: 90
   num_square_width: 16
   num_square_height: 9
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.858353,     -0.0339518,     0.511935,     2174.6400,
         -0.222646,     -0.874309,     -0.431292,      -59.2333,
          0.462233,     -0.484181,      0.742906,     2656.7700,
          0.0,           0.0,           0.0,             1.0    ]
