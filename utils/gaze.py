from typing import Tuple

import cv2
import torch
import numpy as np

from utils.templates import (
    head_pose_indices_MPIIGAZE,
    face_mesh_template_3D_MPIIGAZE,
    face_mesh_template_3D_MEDIAPIPE_mm,
    face_mesh_template_3D_MEDIAPIPE_mm_11p,
)


def normalize(vector: np.ndarray) -> np.ndarray:
    """Normalize a vector to unit length."""
    return vector / np.linalg.norm(vector)


def pitchyaw_to_3d_vector_torch(pitchyaw: torch.Tensor) -> torch.Tensor:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors.

    Args:
        pitchyaw: Tensor of shape (N, 2), where [:, 0] = pitch, and [:, 1] = yaw (in radians)

    Returns:
        Tensor of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -torch.cos(pitch) * torch.sin(yaw)
    y = -torch.sin(pitch)
    z = -torch.cos(pitch) * torch.cos(yaw)

    return torch.stack([x, y, z], dim=1)


def pitchyaw_to_3d_vector_numpy(pitchyaw: np.ndarray) -> np.ndarray:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors (NumPy version).

    Args:
        pitchyaw: Array of shape (N, 2), where [:, 0] = pitch, and [:, 1] = yaw (in radians)

    Returns:
        Array of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = -np.cos(pitch) * np.cos(yaw)

    return np.stack([x, y, z], axis=1)


def pitchyaw_to_gaze_vector(pitch: np.ndarray, yaw: np.ndarray) -> np.ndarray:
    """
    Convert 2D pitch and yaw angles to a 3D gaze direction vector.

    Args:
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians

    Returns:
        3D gaze direction vector as numpy array [x, y, z]
    """
    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = -np.cos(pitch) * np.cos(yaw)
    
    return np.array([x, y, z])


def gaze_vector_to_pitchyaw(gaze_vector: np.ndarray) -> np.ndarray:
    """
    Convert a 3D gaze vector to pitch and yaw angles in radians.

    Args:
        gaze_vector: 3D gaze direction vector as numpy array [x, y, z]

    Returns:
        - pitch: vertical angle in radians
        - yaw: horizontal angle in radians
    """
    x = gaze_vector[0]
    y = gaze_vector[1]
    z = gaze_vector[2]
    
    pitch = np.arcsin(-y)
    yaw = np.arctan2(-x, -z)
    
    return pitch, yaw


def validate_coordinate_system(x_axis, y_axis, z_axis, threshold=1e-6):
    """
    Validate that the three axes form a proper orthogonal coordinate system.

    A valid coordinate system requires:
    1. All axes are unit vectors (length = 1)
    2. All axes are orthogonal to each other (dot product = 0)
    3. The axes follow the right-hand rule (z = x @ y)

    Args:
        x_axis: X-axis unit vector
        y_axis: Y-axis unit vector
        z_axis: Z-axis unit vector
        threshold: Maximum allowed deviation from orthogonality

    Returns:
        is_valid: Boolean indicating if the coordinate system is valid
        issues: List of detected issues (empty if valid)
    """
    issues = []

    # Check if all vectors are unit vectors (length ≈ 1)
    for name, axis in [("X", x_axis), ("Y", y_axis), ("Z", z_axis)]:
        length = np.linalg.norm(axis)
        if abs(length - 1.0) > threshold:
            issues.append(f"{name}-axis is not a unit vector (length = {length:.6f})")

    # Check orthogonality between all pairs of axes
    xy_dot = np.abs(np.dot(x_axis, y_axis))
    yz_dot = np.abs(np.dot(y_axis, z_axis))
    zx_dot = np.abs(np.dot(z_axis, x_axis))

    if xy_dot > threshold:
        issues.append(f"X and Y axes are not orthogonal (dot product = {xy_dot:.6f})")
    if yz_dot > threshold:
        issues.append(f"Y and Z axes are not orthogonal (dot product = {yz_dot:.6f})")
    if zx_dot > threshold:
        issues.append(f"Z and X axes are not orthogonal (dot product = {zx_dot:.6f})")

    # Check right-hand rule: z should equal x × y
    expected_z = np.cross(x_axis, y_axis)
    expected_z = normalize(expected_z)
    z_diff = np.linalg.norm(z_axis - expected_z)

    if z_diff > threshold:
        issues.append(f"Z-axis does not follow right-hand rule (difference = {z_diff:.6f})")

    return len(issues) == 0, issues


def validate_gaze_vector_recovering(
    gaze_vector: np.ndarray,
    rotation_matrix: np.ndarray,
    atol: float = 1e-5,
    verbose: bool = False
) -> None:
    """
    Verify the consistency of gaze vector transformations through forward and inverse operations.

    This function performs a round-trip test to ensure that:
    1. Converting gaze vector to pitch/yaw angles
    2. Converting pitch/yaw back to gaze vector
    3. Transforming between coordinate systems

    Results in the original gaze vector (within numerical tolerance).

    Args:
        gaze_vector: Normalized gaze vector in the original coordinate system
        rotation_matrix: Rotation matrix used for coordinate transformation
        atol: Absolute tolerance for numerical comparisons
        verbose: Whether to print detailed comparison results

    Raises:
        AssertionError: If the round-trip transformation exceeds tolerance
    """
    # 1. Rotate it to get normalized gaze (camera-centric)
    normalized_gaze = np.dot(rotation_matrix, gaze_vector)
    normalized_gaze = normalize(normalized_gaze)

    # Compute pitch and yaw from normalized gaze
    pitch = np.arcsin(-normalized_gaze[1])
    yaw = np.arctan2(-normalized_gaze[0], -normalized_gaze[2])

    # 2. Inverse computation: recover normalized_gaze from the pitch and yaw angles
    recovered_normalized_gaze = pitchyaw_to_gaze_vector(pitch, yaw)
    recovered_normalized_gaze = normalize(recovered_normalized_gaze)

    # 3. Inverse computation: recover original gaze vector
    recovered_gaze_vector = np.dot(rotation_matrix.T, recovered_normalized_gaze)
    recovered_gaze_vector = normalize(recovered_gaze_vector)

    # 4. Compare with original normalized vector
    cosine_similarity = np.dot(gaze_vector.flatten(), recovered_gaze_vector.flatten())
    angle_diff_deg = np.degrees(np.arccos(np.clip(cosine_similarity, -1.0, 1.0)))

    # 5. Verify
    assert np.allclose(normalized_gaze, recovered_normalized_gaze, atol=atol), f"{normalized_gaze} != {recovered_normalized_gaze}"
    assert np.allclose(cosine_similarity, 1.0, atol=atol), f"{cosine_similarity} != {1.0}"
    assert np.allclose(angle_diff_deg, 0.0, atol=atol), f"{angle_diff_deg} != {0.0}"

    if verbose:
        print("Original gaze vector (normalized):\n", gaze_vector)
        print("Recovered gaze vector:\n", recovered_gaze_vector)
        print("Cosine similarity:", cosine_similarity)
        print("Angular difference (deg):", angle_diff_deg)


def rotation_alignment_error(R1: np.ndarray, R2: np.ndarray) -> float:
    """
    Computes L2 norm of (I - R1 * R2) where R1, R2 are 3x3 rotation matrices.

    This measures how close the composite rotation R1*R2 is to the identity.

    Args:
        R1: Rotation matrix (3x3)
        R2: Rotation matrix (3x3)

    Returns:
        Frobenius norm (L2) of (I - R1 @ R2)
    """
    I = np.eye(3)
    R_comp = R1 @ R2.T
    diff = I - R_comp
    error = np.linalg.norm(diff, ord='fro')  # Frobenius norm = L2 for matrices
    
    # Trace-based angular difference (in radians)
    trace = np.clip(np.trace(R_comp), -1.0, 3.0)  # numerical safety
    angle_rad = np.arccos((trace - 1) / 2)
    angle_deg = np.degrees(angle_rad)
    return error, angle_deg


def get_invSR_vec(normalized_gaze_vector, rotation_matrix, scaling_matrix, scaled=True):
    """ Compute the inverse of the scaling and rotation matrix
    and apply it to the normalized gaze vector to get the denormalized gaze vector 
    """
    if scaled == True:
        SR_matrix = np.dot(scaling_matrix, rotation_matrix)
        invSR_matrix = np.linalg.inv(SR_matrix)
        denormalized_gaze_vector = np.dot(invSR_matrix, normalized_gaze_vector)
    else:
        denormalized_gaze_vector = np.dot(rotation_matrix.T, normalized_gaze_vector)
    
    invSR_vec = cv2.Rodrigues(invSR_matrix)[0].T[0]
    denormalized_gaze_vector = normalize(denormalized_gaze_vector)
    
    # R_back, _ = cv2.Rodrigues(invSR_vec)

    # def project_to_so3(R):
    #     U, S, Vt = np.linalg.svd(R)
    #     R_hat = U @ Vt
    #     if np.linalg.det(R_hat) < 0:
    #         U[:, -1] *= -1
    #         R_hat = U @ Vt
    #     return R_hat

    # R_in = invSR_matrix
    # R_in = project_to_so3(R_in)           # fix the input
    # rvec, _ = cv2.Rodrigues(R_in)         # matrix -> vector
    # R_back, _ = cv2.Rodrigues(rvec)       # vector -> matrix
    # R_back = project_to_so3(R_back)       # enforce orthonormality

    # err = np.linalg.norm(R_in - R_back, ord='fro')
    # angle_err_deg = np.rad2deg(np.arccos(np.clip((np.trace(R_in.T @ R_back) - 1)/2, -1, 1)))
    # print("Frobenius error:", err, " Angle error (deg):", angle_err_deg)

    # print("Original:\n", R_in)
    # print("Back:\n", R_back)
    # exit()

    return invSR_vec, denormalized_gaze_vector


def normalize_eyes_landmarks(image, landmarks_2D, transformation_matrix, draw_points=False):
    # --- Transform original eye pixel coordinates ---
    # Convert 2D pixel coordinates to homogeneous coordinates (x, y, 1)
    face_img_vis = None
    h, w = image.shape[:2]
    
    left_eye_center_2D = landmarks_2D[468]
    right_eye_center_2D = landmarks_2D[473]
    
    original_eye_pixels = np.array([
        left_eye_center_2D,
        right_eye_center_2D
    ], dtype=np.float32).reshape(-1, 1, 2)

    # Apply the transformation matrix
    transformed_eye_pixels = cv2.perspectiveTransform(original_eye_pixels, transformation_matrix)
    
    # Extract the 2D pixel coordinates from the transformed homogeneous coordinates
    normalized_left_eye_pixel = transformed_eye_pixels[0, 0, :2]
    normalized_right_eye_pixel = transformed_eye_pixels[1, 0, :2]

    # Save landmarks image
    if draw_points:
        face_img_vis = image.copy()
        cv2.circle(face_img_vis, (int(normalized_left_eye_pixel[0]), int(normalized_left_eye_pixel[1])), 2, (0, 255, 0), -1)
        cv2.circle(face_img_vis, (int(normalized_right_eye_pixel[0]), int(normalized_right_eye_pixel[1])), 2, (0, 255, 0), -1)
    
    normalized_left_eye_pixel[0] /= w
    normalized_left_eye_pixel[1] /= h
    normalized_right_eye_pixel[0] /= w
    normalized_right_eye_pixel[1] /= h
    
    return face_img_vis, normalized_left_eye_pixel, normalized_right_eye_pixel


def pixel_to_camera_point_multiview(
    point_px: Tuple[int, int],
    monitor_px: Tuple[int, int],
    monitor_mm: Tuple[float, float],
    R_scr2cam: np.ndarray = None,
    tvec_scr2cam_mm: np.ndarray = None,
) -> np.ndarray:
    """
    Convert a 2D pixel coordinate on the screen to a 3D point in camera space.

    Args:
        point_px: (x, y) pixel coordinates on the screen
        monitor_px: (width, height) of the monitor in pixels
        monitor_mm: (width, height) of the monitor in millimeters
        R_scr2cam: Rotation matrix from screen to camera (3x3)
        tvec_scr2cam_mm: Translation vector from screen to camera (3x1)

    Returns:
        3D point in camera coordinate system (shape: 3x1)
    """
    # Extract coordinates and dimensions
    px, py = point_px
    W_px, H_px = monitor_px # Monitor resolution in pixels
    W_mm, H_mm = monitor_mm # Monitor physical dimensions in mm

    # Convert pixel coordinates to millimeters in the screen coordinate system
    # This maps (0, 0) to top-left and (W_px, H_px) to bottom-right of physical screen
    x_mm = px * (W_mm / W_px)   # Scale X coordinate
    y_mm = py * (H_mm / H_px)   # Scale Y coordinate

    # Create 3D point in screen coordinate system (Z=0 is the screen plane)
    screen_pt = np.array([[x_mm], [y_mm], [0.0]], dtype=np.float64)

    # Transform the point from screen to camera coordinate system
    pt_cam = R_scr2cam @ screen_pt + tvec_scr2cam_mm

    return pt_cam


def pixel_to_camera_point(
    point_px: Tuple[int, int],
    monitor_px: Tuple[int, int],
    monitor_mm: Tuple[float, float],
    rvec_scr2cam: np.ndarray = None,
    tvec_scr2cam_mm: np.ndarray = None,
) -> np.ndarray:
    """
    Convert a 2D pixel coordinate on the screen to a 3D point in camera space.

    Args:
        point_px: (x, y) pixel coordinates on the screen
        monitor_px: (width, height) of the monitor in pixels
        monitor_mm: (width, height) of the monitor in millimeters
        rvec_scr2cam: Rotation vector in Rodrigues format (3x1)
        tvec_scr2cam_mm: Translation vector from screen to camera (3x1)

    Returns:
        3D point in camera coordinate system (shape: 3x1)
    """
    # Extract coordinates and dimensions
    px, py = point_px
    W_px, H_px = monitor_px # Monitor resolution in pixels
    W_mm, H_mm = monitor_mm # Monitor physical dimensions in mm

    # Step 1: Convert pixel coordinates to millimeters in the screen coordinate system
    # This maps (0, 0) to top-left and (W_px, H_px) to bottom-right of physical screen
    x_mm = px * (W_mm / W_px)   # Scale X coordinate
    y_mm = py * (H_mm / H_px)   # Scale Y coordinate

    # Create 3D point in screen coordinate system (Z=0 is the screen plane)
    screen_pt = np.array([[x_mm], [y_mm], [0.0]], dtype=np.float64)

    # Step 2: Define the transformation from screen to camera coordinate system
    # This is a fixed transformation based on the physical setup
    # The rotation matrix flips X and Z axes to align with camera coordinates
    if rvec_scr2cam is not None:
        R_scr2cam = cv2.Rodrigues(rvec_scr2cam)[0]
        R_scr2cam = np.round(R_scr2cam).astype(np.float64)
    else:
        R_scr2cam = np.array([
            [-1.0,  0.0,  0.0],  # X-axis is flipped
            [ 0.0,  1.0,  0.0],  # Y-axis stays the same
            [ 0.0,  0.0, -1.0]   # Z-axis is flipped
        ], dtype=np.float64)
    
    # Translation vector from screen origin to camera (in mm)
    if rvec_scr2cam is not None:
        tvec_scr2cam_mm = np.round(tvec_scr2cam_mm).astype(np.float64)
    else:
        tvec_scr2cam_mm = np.array([[290.0], [25.0], [15.0]], dtype=np.float64)

    # Step 3: Transform the point from screen to camera coordinate system
    pt_cam = R_scr2cam @ screen_pt + tvec_scr2cam_mm

    return pt_cam


def get_valid_depth(depth_frame, u, v):
    """Get valid depth value from a depth frame at given pixel coordinates.

    Args:
        depth_frame (np.ndarray): Depth image from depth camera with depth values in (meters)
        u (int): Horizontal pixel coordinate
        v (int): Vertical pixel coordinate

    Returns:
        float: Valid depth value in meters. Returns 0 if no valid depth found.
    """
    h, w = depth_frame.shape
    
    for window in [1, 3, 5]:
        half_win = window // 2
        # Extract patch and remove invalid values
        patch = depth_frame[
            max(0, v - half_win):min(h, v + half_win + 1),
            max(0, u - half_win):min(w, u + half_win + 1)
        ]
        valid_values = patch[np.isfinite(patch) & (patch > 0)]
        
        if valid_values.size > 0:
            return float(np.nanmedian(valid_values))
    return 0    # fallback if nothing found


def get_face_landmarks_in_ccs_from_depth(
    face_landmarks_2D: np.ndarray,
    depth_frame: np.ndarray,
    camera_matrix: np.ndarray,
    filter_depth: bool = True,
    depth_range: tuple = (0.1, 2.0),
    depth_mm2m: float = 1000.0,
) -> np.ndarray:
    """
    Convert 2D facial landmarks to 3D camera coordinate system using depth information.

    This function takes 2D pixel coordinates of facial landmarks and uses the corresponding
    depth values to compute their 3D positions in the camera coordinate system.

    Args:
        face_landmarks_2D: Array of 2D facial landmarks with shape (N, 2) containing [u, v] pixel coordinates
        depth_frame: Depth image from depth camera (height x width array) with depth values in millimeters
        camera_matrix: 3x3 intrinsic camera matrix containing focal lengths and principal point
        filter_depth: Whether to filter out depth values outside the specified range
        depth_range: Tuple (min, max) specifying the valid depth range in meters
        depth_mm2m: Conversion factor from millimeters to meters

    Returns:
        3D facial landmarks in camera coordinate system with shape (3, N) containing [X, Y, Z] coordinates
    """
    height, width = depth_frame.shape
    
    fx = camera_matrix[0, 0]  # Focal length in x
    fy = camera_matrix[1, 1]  # Focal length in y
    cx = camera_matrix[0, 2]  # Principal point x
    cy = camera_matrix[1, 2]  # Principal point y

    landmarks_3D = []
    for (u, v) in face_landmarks_2D:
        # Ensure integer pixel coordinates and valid depth retrieval
        u, v = int(round(u)), int(round(v))

        # Out-of-bounds handling
        if not ((0 <= u < width) and (0 <= v < height)):
            landmarks_3D.append([np.nan, np.nan, np.nan])
            continue

        # Get the depth at the landmark pixel (millimeters)
        if filter_depth:
            d = get_valid_depth(depth_frame, u, v)
        else:
            d = depth_frame[v, u]

        # If depth is invalid or unreasonably near/far, skip
        if (d == 0) or not (depth_range[0] <= d/depth_mm2m <= depth_range[1]):
            landmarks_3D.append([np.nan, np.nan, np.nan])
            continue

        # Convert from pixel coordinates (u, v) plus depth d to 3D coordinates
        X = (u - cx) * d / fx
        Y = (v - cy) * d / fy
        Z = d
        landmarks_3D.append([X, Y, Z])

    return np.array(landmarks_3D).T  # Shape: (3, N)


def is_invalid_vector(v):
    """Check if a vector is invalid (all zeros, contains NaN, or contains Inf)"""
    v = np.asarray(v)
    return (
        np.allclose(v, 0.0, atol=1e-6)
        or np.isnan(v).any()
        or np.isinf(v).any()
    )


def draw_head_pose_axes(image, rvec, tvec, K, D, axis_length=80, thickness=3):
    """ Visualize head pose by projecting 3D axes onto the image """
    axes_3d = np.array([
        [axis_length, 0, 0],  # X axis (white)
        [0, axis_length, 0],  # Y axis (black)
        [0, 0, axis_length]   # Z axis (green)
    ], dtype=np.float32)
    origin_3d = np.array([[0, 0, 0]], dtype=np.float32)
    
    # Project 3D points to image plane
    origin_2d, _ = cv2.projectPoints(
        origin_3d,
        rvec,
        tvec,
        K,
        D
    )
    axes_2d, _ = cv2.projectPoints(
        axes_3d,
        rvec,
        tvec,
        K,
        D
    )
    
    # Draw axes on the image
    p1 = tuple(origin_2d[0].ravel().astype(int))
    for i, color in zip(reversed(range(3)), [(255,255,255), (0,0,0), (0,255,0)]):  # X, Y, Z
        p2 = tuple(axes_2d[i].ravel().astype(int))
        cv2.line(image, p1, p2, color, thickness)


def solvePnP_refine(model_points, image_points, camera_matrix, dist_coeff):
    """ Solve PnP to get rotation and translation vectors """
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        camera_matrix,
        dist_coeff,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit
    
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            model_points[inliers[:, 0]],
            image_points[inliers[:, 0]],
            camera_matrix,
            dist_coeff,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    return rvec, tvec


def compute_head_pose_from_landmarks(landmarks_3D: np.ndarray, validate: bool = True) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute head rotation matrix from 3D facial landmarks in camera coordinate system.

    This function estimates head pose by:
    1. Identifying key facial landmarks (eyes, mouth corners)
    2. Constructing an orthogonal coordinate system based on facial geometry
    3. Creating a rotation matrix representing head orientation relative to camera
    4. Converting the rotation matrix to a compact rotation vector (Rodrigues format)

    The coordinate system is defined as:
    - X-axis: From right eye to left eye (horizontal facial axis)
    - Y-axis: From eye center to mouth center (vertical facial axis)
    - Z-axis: Perpendicular to face plane (forward direction)

    Args:
        landmarks_3D: 3D facial landmarks with shape (3, N) in camera coordinate system
        validate: Whether to validate the orthogonality of the computed coordinate system

    Returns:
        Tuple containing:
        - rvec: Head rotation vector in Rodrigues format (3x1)
        - R: Head rotation matrix (3x3)
    """
    # Define landmark indices for key facial features
    right_eye_indices = [33, 133]      # Subject's right eye (from camera view: left)
    left_eye_indices = [362, 263]      # Subject's left eye (from camera view: right)
    mouth_indices = [61, 291]          # Mouth corners

    # Compute key points by averaging landmark positions
    right_eye = np.mean(landmarks_3D[:, right_eye_indices], axis=1)
    left_eye = np.mean(landmarks_3D[:, left_eye_indices], axis=1)
    mouth_center = np.mean(landmarks_3D[:, mouth_indices], axis=1)
    eye_center = 0.5 * (left_eye + right_eye)

    # Step 1: Construct initial coordinate system
    # X-axis: from right eye to left eye (horizontal axis)
    x_axis = left_eye - right_eye
    x_axis = normalize(x_axis)

    # Y-axis: from eye center to mouth center (vertical axis)
    y_axis = mouth_center - eye_center
    y_axis = normalize(y_axis)

    # Step 2: Ensure orthogonality using cross products
    # Z-axis: perpendicular to both X and Y (forward direction)
    z_axis = np.cross(x_axis, y_axis)
    z_axis = normalize(z_axis)

    # Re-orthogonalize Y-axis to ensure strict orthogonality
    y_axis = np.cross(z_axis, x_axis)
    y_axis = normalize(y_axis)

    # Step 3: Validate the coordinate system if requested
    if validate:
        is_valid, issues = validate_coordinate_system(x_axis, y_axis, z_axis)

        if not is_valid:
            print("WARNING: Invalid coordinate system detected:")
            for issue in issues:
                print(f"  - {issue}")
            print("Proceeding with corrected axes.")

    # Step 4: Create rotation matrix and convert to rotation vector
    # Rotation matrix: columns are the axes of face in camera space
    R = np.stack([x_axis, y_axis, z_axis], axis=1)  # Shape (3, 3)

    # Convert rotation matrix to rotation vector (Rodrigues)
    rvec, _ = cv2.Rodrigues(R)

    return rvec, R


def compute_head_pose_solvePnP(
    landmarks: np.ndarray,
    camera_matrix: np.ndarray,
    dist_coeff: np.ndarray,
    mediapipe=False
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Estimate head pose using PnP (Perspective-n-Point) algorithm.

    This function estimates head pose by solving the PnP problem using:
    1. 2D facial landmarks in the image
    2. Corresponding 3D facial landmark template
    3. Camera intrinsic parameters

    Args:
        landmarks: Array of facial landmarks (shape varies based on mediapipe flag)
        mediapipe: If True, use MediaPipe face mesh template; if False, use MPII-Gaze template

    Returns:
        Tuple containing:
        - rvec: Head rotation vector in Rodrigues format (3x1)
        - R: Head rotation matrix (3x3)
    """
    if mediapipe == True:
        # Get 2D image points and 3D Face model template (mediaPipe)
        image_points = landmarks[:468, :2].astype(np.float32)                       # (x, y)
        model_points = face_mesh_template_3D_MEDIAPIPE_mm[:, :].astype(np.float32)     # (x, y, z)
    elif mediapipe == False:
        # Get 2D image points and 3D Face model template (MPII-Gaze)
        image_points = landmarks[head_pose_indices_MPIIGAZE, :2].astype(np.float32) # (x, y)
        model_points = face_mesh_template_3D_MPIIGAZE.T                             # (x, y, z)

    # Solve PnP to get rotation and translation vectors
    rvec, tvec = solvePnP_refine(model_points, image_points, camera_matrix, dist_coeff)

    # Convert rotation vector to Euler angles
    R = cv2.Rodrigues(rvec)[0]

    return rvec, R


def get_face_landmarks_in_ccs_from_solvePnP(landmarks_2D, K, D):
    """ Solve PnP to get rotation and translation vectors """
    image_points = landmarks_2D[:468, :2].astype(np.float32)                       # (x, y)
    model_points = face_mesh_template_3D_MEDIAPIPE_mm[:, :].astype(np.float32)  # (x, y, z)
        
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        K,
        D,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit

    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            model_points[inliers[:, 0]],
            image_points[inliers[:, 0]],
            K,
            D,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    
    R = cv2.Rodrigues(rvec)[0]
    landmarks_3D = np.dot(R, model_points.T) + tvec

    return landmarks_3D, rvec, R, tvec # Shape: (3, N)


def get_normalization_matrices(
    center_point: np.ndarray,
    distance_norm: int,
    focal_norm: int,
    image_output_size: Tuple[int, int],
    head_rotation_matrix: np.ndarray,
    camera_matrix: np.ndarray,
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Calculate matrices needed for gaze data normalization.

    This function computes the rotation, scaling, and transformation matrices
    required to normalize face/eye images to a canonical view.

    Args:
        center_point: 3D position of the center point (face or eye center) in camera coordinates
        distance_norm: Target normalized distance between camera and face/eye
        focal_norm: Target normalized focal length for the normalized camera
        image_output_size: Dimensions (width, height) of the output normalized image
        head_rotation_matrix: 3x3 rotation matrix representing head pose
        camera_matrix: Intrinsic camera matrix (3x3) containing focal lengths and principal point

    Returns:
        Tuple containing:
        - rotation_matrix: 3x3 matrix to rotate points to normalized view
        - scaling_matrix: 3x3 matrix to scale distances
        - transformation_matrix: 3x3 matrix for the complete image transformation
    """
    # Step 1: Calculate distance and scaling factor
    # The actual distance between center point and camera
    actual_distance = np.linalg.norm(center_point)
    # Scale factor to achieve the target normalized distance
    z_scale = distance_norm / actual_distance

    # Step 2: Create normalized camera matrix with desired parameters
    normalized_camera_matrix = np.array([
        [focal_norm,    0.0,            image_output_size[0] / 2],  # fx and cx
        [0.0,           focal_norm,     image_output_size[1] / 2],  # fy and cy
        [0.0,           0.0,            1.0                     ],  # homogeneous coordinate
    ])

    # Step 3: Create scaling matrix (only scales in Z direction)
    scaling_matrix = np.array([
        [1.0, 0.0, 0.0    ],  # No scaling in X
        [0.0, 1.0, 0.0    ],  # No scaling in Y
        [0.0, 0.0, z_scale],  # Scale in Z to achieve normalized distance
    ])

    # Step 4: Compute the new coordinate system axes
    # Forward axis (Z) - direction from camera to center point
    forward_axis = (center_point / actual_distance)

    # Down axis (Y) - perpendicular to forward and head's right direction
    down_axis = np.cross(forward_axis, head_rotation_matrix[:, 0])
    down_axis = normalize(down_axis)

    # Right axis (X) - perpendicular to down and forward
    right_axis = np.cross(down_axis, forward_axis)
    right_axis = normalize(right_axis)

    # Step 5: Create rotation matrix from the three axes
    rotation_matrix = np.asarray([right_axis, down_axis, forward_axis])

    # Step 6: Compute the complete transformation matrix
    # This combines: original camera → 3D world → rotated 3D world → scaled 3D world → normalized camera
    transformation_matrix = np.dot(
        np.dot(normalized_camera_matrix, scaling_matrix),
        np.dot(rotation_matrix, np.linalg.inv(camera_matrix))
    )

    return rotation_matrix, scaling_matrix, transformation_matrix


def normalize_single_image(
    image: np.ndarray,
    head_rotation_matrix: np.ndarray,
    gaze_vector: np.ndarray,
    center_point: np.ndarray,
    camera_matrix: np.ndarray,
    focal_norm: int = 411.43, # 500
    distance_norm: int = 600,
    is_eye: bool = True,
    scale = True
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Normalize an image to a canonical view based on 3D geometry.

    This function performs data normalization for gaze estimation by:
    1. Transforming the image to a canonical view based on head pose
    2. Normalizing the gaze vector to the new coordinate system

    Args:
        image: Original RGB image
        head_rotation_matrix: Head rotation matrix (3x3)
        gaze_vector: Normalized gaze vector in the original camera space
        center_point: 3D point to center the normalized view on (eye or face center)
        camera_matrix: Intrinsic camera matrix (3x3)
        is_eye: If True, use parameters for eye normalization; if False, use face parameters

    Returns:
        Tuple containing:
        - normalized_image: Warped and normalized image
        - gaze_vector: Gaze vector in the original camera space
        - normalized_gaze: Gaze vector in the normalized space
        - rotation_matrix: Rotation matrix used for normalization
        - transformation_matrix: Complete transformation matrix used for image warping
    """
    # Step 1: Set normalization parameters based on whether we're normalizing an eye or face
    # Different parameters for eye vs face normalization
    if is_eye:
        image_output_size = (26, 15)  # Width, height for eye image
    else:
        image_output_size = (96, 96)  # Width, height for face image
    
    # Step 2: Compute normalization matrices
    rotation_matrix, scaling_matrix, transformation_matrix = get_normalization_matrices(
        center_point,
        distance_norm,
        focal_norm,
        image_output_size,
        head_rotation_matrix,
        camera_matrix,
    )
    normalization_matrices = {"R": rotation_matrix, "S": scaling_matrix, "W": transformation_matrix}
    
    # Step 3: Warp the image to normalized view
    normalized_image = cv2.warpPerspective(
        image, transformation_matrix, image_output_size
    )

    # Step 4: Normalize the gaze vector if provided
    if gaze_vector is not None:
        if scale:
            normalized_gaze = np.dot(np.dot(scaling_matrix, rotation_matrix), gaze_vector)
            normalized_gaze = normalize(normalized_gaze)
        else:
            normalized_gaze = np.dot(rotation_matrix, gaze_vector)
            normalized_gaze = normalize(normalized_gaze)
    else:
        # If no gaze target provided, return zero vector
        gaze_vector = np.zeros(3)
        normalized_gaze = np.zeros(3)

    return normalized_image, gaze_vector, normalized_gaze, normalization_matrices
