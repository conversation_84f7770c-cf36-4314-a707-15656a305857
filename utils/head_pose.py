import os
import glob
import json
import argparse
from pathlib import Path
from collections import defaultdict

import cv2
import numpy as np
from tqdm import tqdm

from ultralytics.utils.utils import CameraCalibrator
from ultralytics.utils.face_templates import face_mesh_template_3D_MEDIAPIPE_mm, face_mesh_template_3D_MEDIAPIPE_mm_11p


HD = (1280, 720)
FHD = (1920, 1080)


def compute_reprojection_error(proj_points, image_points):
    # Compute per-point Euclidean error
    errors = np.linalg.norm(image_points - proj_points, axis=1)

    # Root Mean Square (RMS) reprojection error
    rms_error = np.sqrt(np.mean(errors**2))

    return rms_error


def get_bbox(original, keypoints, pad=200):
    """Get bounding box around keypoints with padding."""
    h, w = original.shape[:2]

    # Handle case where keypoints might be 2D projected points
    if keypoints.ndim == 2 and keypoints.shape[1] >= 2:
        if keypoints.shape[1] == 3:
            # Use only visible keypoints if visibility info is available
            visible_mask = keypoints[:, 2] == 2
            if np.any(visible_mask):
                points = keypoints[visible_mask, :2]
            else:
                points = keypoints[:, :2]
        else:
            points = keypoints[:, :2]
    else:
        points = keypoints

    if len(points) == 0:
        # Return full image if no points
        return 0, w, 0, h

    # Get center of points
    cx = int(np.mean(points[:, 0]))
    cy = int(np.mean(points[:, 1]))

    # Ensure bounding box is within image bounds
    x0 = max(cx - pad, 0)
    y0 = max(cy - pad, 0)
    x1 = min(cx + pad, w)
    y1 = min(cy + pad, h)

    # Ensure we have a valid bounding box
    if x1 <= x0 or y1 <= y0:
        return 0, w, 0, h

    return x0, x1, y0, y1


def load_camera_params(cam_params_path):
    # Load camera poses
    cam_params = cv2.FileStorage(cam_params_path, cv2.FILE_STORAGE_READ)

    cam_names = [
        "cam_0", "cam_1", "cam_2", "cam_3", "cam_4", "cam_5", "cam_6", "cam_7",     # OMS Fisheye
        "cam_8",                                                                    # Anker Pinhole
        "cctv_net_1", "cctv_net_2",                                                 # CCTV Pinhole
        "rs_146222252273", "rs_146222251797",                                       # RealSense Pinhole
        "tof_240600110",                                                            # TOF
    ]
   
    tv_names = ["tv_1", "tv_2", "tv_3"]
    
    # Display cameras
    camera_params = {}
    for cam_name in cam_names:
        # Get camera pose
        K = cam_params.getNode(cam_name).getNode("camera_matrix").mat()
        D = cam_params.getNode(cam_name).getNode("distortion_vector").mat()
        cam_pose = cam_params.getNode(cam_name).getNode("camera_pose_matrix").mat()

        # Get distortion type (0=pinhole, 1=fisheye)
        distortion_type_node = cam_params.getNode(cam_name).getNode("distortion_type")
        distortion_type = int(distortion_type_node.real()) if not distortion_type_node.empty() else 1

        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T

        # Get image size
        img_width = int(cam_params.getNode(cam_name).getNode("img_width").real())
        img_height = int(cam_params.getNode(cam_name).getNode("img_height").real())
        
        # Store info
        camera_params[cam_name] = {
            "K": K,
            "D": D,
            "pose": cam_pose,
            "R": cam_R,
            "tvec": cam_tvec,
            "img_width": img_width,
            "img_height": img_height,
            "distortion_type": distortion_type,
        }
        
    tv_params = {}
    for tv_name in tv_names:
        # Get TV dimensions
        width_px = cam_params.getNode(tv_name).getNode("width_px").real()
        height_px = cam_params.getNode(tv_name).getNode("height_px").real()
        monitor_px = (width_px, height_px)
        
        # Get TV dimensions
        width_mm = cam_params.getNode(tv_name).getNode("width_mm").real()
        height_mm = cam_params.getNode(tv_name).getNode("height_mm").real()
        monitor_mm = (width_mm, height_mm)
        
        square_size_mm = cam_params.getNode(tv_name).getNode("square_size_mm").real()
        num_square_width = cam_params.getNode(tv_name).getNode("num_square_width").real()
        num_square_height = cam_params.getNode(tv_name).getNode("num_square_height").real()
        
        # Get camera pose
        tv_pose = cam_params.getNode(tv_name).getNode("camera_pose_matrix").mat()
        
        # Get rotation and translation
        tv_R = tv_pose[0:3, 0:3]
        tv_tvec = np.asarray([tv_pose[0:3, 3]]).T
        
        # Store info
        tv_params[tv_name] = {
            "monitor_px": monitor_px,
            "monitor_mm": monitor_mm,
            "square_size_mm": square_size_mm,
            "num_square_width": num_square_width,
            "num_square_height": num_square_height,
            "tv_pose": tv_pose,
            "tv_R": tv_R,
            "tv_tvec": tv_tvec,
        }
    return camera_params, tv_params


def match_images_and_keypoints(image_paths, jsons_paths):
    """ Match images and keypoints from jsons """
    scene_groups = defaultdict(lambda: defaultdict(lambda: {'image_path': None, 'keypoints': None}))

    for img_path in image_paths:
        base_name = os.path.basename(img_path)
        cam_name = img_path.split("/")[-2]
        scene_groups[base_name][cam_name]['image_path'] = img_path
    
    # Load and organize keypoints for each image
    for json_path in jsons_paths:
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        cam_name = json_path.split("/")[-2]
        # Create mappings
        image_id_to_filename = {img["id"]: img["file_name"] for img in data["images"]}
        image_id_to_keypoints = {ann["image_id"]: ann["keypoints"] for ann in data["annotations"]}

        # Add keypoints to corresponding image groups
        for img_id, filename in image_id_to_filename.items():
            if img_id in image_id_to_keypoints:
                base_name = os.path.basename(filename)
                
                if base_name in scene_groups:
                    keypoints = np.array(image_id_to_keypoints[img_id]).reshape(-1, 3)
                    keypoints = np.round(keypoints).astype(np.int32)
                    
                    # remove rows with indices 7 and 8 (ears)
                    keypoints = np.delete(keypoints, [7, 8], axis=0)
                    scene_groups[base_name][cam_name]['keypoints'] = keypoints
    
    return scene_groups


def find_origin_camera_with_most_visible_keypoints(camera_groups):
    """
    Find the camera with the most visible keypoints to use as origin.

    Args:
        camera_groups: Dictionary of camera_name -> sample_group

    Returns:
        origin_cam_name: Name of camera with most visible keypoints
        max_visible_count: Number of visible keypoints in origin camera
    """
    max_visible_count = 0
    origin_cam_name = None

    for cam_name, sample_group in camera_groups.items():
        if sample_group['keypoints'] is not None:
            # Count visible keypoints (z == 2)
            visible_count = np.sum(sample_group['keypoints'][:, 2] == 2)

            if visible_count > max_visible_count:
                max_visible_count = visible_count
                origin_cam_name = cam_name

    return origin_cam_name, max_visible_count


def get_face_landmarks_in_ccs_from_solvePnP(model_points, image_points, K, D):
    """ Solve PnP to get rotation and translation vectors """
    rvec, tvec = None, None
    success, rvec, tvec, inliers = cv2.solvePnPRansac(
        model_points,
        image_points,
        K,
        D,
        rvec=rvec,
        tvec=tvec,
        useExtrinsicGuess=True,
        flags=cv2.SOLVEPNP_EPNP
    )   # Initial fit
    
    if inliers is not None:
        second_fit_model_points = model_points[inliers[:, 0]]
        second_fit_image_points = image_points[inliers[:, 0]]
    else:
        second_fit_model_points = model_points
        second_fit_image_points = image_points
    
    for _ in range(10):
        success, rvec, tvec = cv2.solvePnP(
            second_fit_model_points,
            second_fit_image_points,
            K,
            D,
            rvec=rvec,
            tvec=tvec,
            useExtrinsicGuess=True,
            flags=cv2.SOLVEPNP_ITERATIVE
    )   # Second fit for higher accuracy
    
    return rvec, tvec   # Shape: (3, N)


def get_pose_for_origin(origin_sample, camera_params, origin_cam_name):
    """
    Transform pose from origin camera (with most visible keypoints) to target camera.

    Args:
        origin_sample: Sample group from origin camera
        target_sample: Sample group from target camera
        camera_params: Camera calibration parameters
        origin_cam_name: Name of origin camera
        target_cam_name: Name of target camera

    Returns:
        target_image: Target image with projected pose
        target_keypoints: Target keypoints
        projected_points: 2D projected points in target image
        pose_info: Dictionary with pose estimation info
    """
    # Get image paths
    origin_image_path = origin_sample['image_path']
    
    # Get images
    origin_image = cv2.imread(origin_image_path)

    # Get keypoints
    origin_keypoints = origin_sample['keypoints'].copy()

    # Get camera parameters
    origin_params = camera_params[origin_cam_name]

    # Check if cameras have distortion type info, default to fisheye (1) if not present
    origin_distortion_type = origin_params.get("distortion_type", 1)

    # Undistort origin camera
    origin_calibrator = CameraCalibrator(origin_params["K"], origin_params["D"],
                                       is_fisheye=(origin_distortion_type == 1))
    origin_image = origin_calibrator.undistort_image(origin_image, 0.0)[0]
    origin_keypoints = origin_calibrator.undistort_point(origin_keypoints)
    K_origin, D_origin = origin_calibrator.camera_matrix_undistorted, origin_calibrator.dist_coeffs_undistorted

    # Get visible keypoints from origin camera for pose estimation
    visible_ids = np.where(origin_keypoints[:, 2] == 2)[0]
    image_points = origin_keypoints[visible_ids, :2].astype(np.float32)

    # Get corresponding 3D model points
    model_points = face_mesh_template_3D_MEDIAPIPE_mm_11p
    model_points = model_points[visible_ids, :3]

    # Solve PnP on origin camera to get pose
    rvec_origin, tvec_origin = get_face_landmarks_in_ccs_from_solvePnP(
        model_points, image_points, K_origin, D_origin
    )

    # Draw coordinate axes on origin image
    cv2.drawFrameAxes(origin_image, K_origin, D_origin, rvec_origin, tvec_origin, 50, thickness=1)

    # Project points back to origin image for verification
    proj_points_origin = cv2.projectPoints(
        model_points, rvec_origin, tvec_origin, K_origin, D_origin
    )[0].reshape(-1, 2)
    reprojection_error_origin = compute_reprojection_error(proj_points_origin, image_points)

    # Draw projected points on origin
    for p in proj_points_origin:
        cv2.circle(origin_image, (int(p[0]), int(p[1])), 2, (255, 255, 255), -1)

    pose_info = {
        'origin_cam_name': origin_cam_name,
        'target_cam_name': None,
        'visible_keypoints_count': len(visible_ids),
        'origin_image_path': origin_image_path,
        'target_image_path': None,
        'origin_image': origin_image,
        'target_image': None,
        'K_origin': K_origin,
        'D_origin': D_origin,
        'K_target': None,
        'D_target': None,
        'rvec_origin': rvec_origin,
        'tvec_origin': tvec_origin,
        'rvec_target': None,
        'tvec_target': None,
        'origin_keypoints': origin_keypoints,
        'target_keypoints': None,
        'proj_points_origin': proj_points_origin,
        'proj_points_target': None,
        'reprojection_error_origin': reprojection_error_origin,
        'reprojection_error_target': None
    }

    return pose_info


def transform_pose_from_origin_to_target(origin_sample, target_sample, camera_params, origin_cam_name, target_cam_name):
    """
    Transform pose from origin camera (with most visible keypoints) to target camera.

    Args:
        origin_sample: Sample group from origin camera
        target_sample: Sample group from target camera
        camera_params: Camera calibration parameters
        origin_cam_name: Name of origin camera
        target_cam_name: Name of target camera

    Returns:
        target_image: Target image with projected pose
        target_keypoints: Target keypoints
        projected_points: 2D projected points in target image
        pose_info: Dictionary with pose estimation info
    """
    # Get image paths
    origin_image_path = origin_sample['image_path']
    target_image_path = target_sample['image_path']
    
    # Get images
    origin_image = cv2.imread(origin_image_path)
    target_image = cv2.imread(target_image_path)

    # Get keypoints
    origin_keypoints = origin_sample['keypoints'].copy()
    target_keypoints = target_sample['keypoints'].copy()

    # Get camera parameters
    origin_params = camera_params[origin_cam_name]
    target_params = camera_params[target_cam_name]

    # Check if cameras have distortion type info, default to fisheye (1) if not present
    origin_distortion_type = origin_params.get("distortion_type", 1)
    target_distortion_type = target_params.get("distortion_type", 1)

    # Undistort origin camera
    origin_calibrator = CameraCalibrator(origin_params["K"], origin_params["D"],
                                       is_fisheye=(origin_distortion_type == 1))
    origin_image = origin_calibrator.undistort_image(origin_image, 0.0)[0]
    origin_keypoints = origin_calibrator.undistort_point(origin_keypoints)
    K_origin, D_origin = origin_calibrator.camera_matrix_undistorted, origin_calibrator.dist_coeffs_undistorted

    # Undistort target camera
    target_calibrator = CameraCalibrator(target_params["K"], target_params["D"],
                                       is_fisheye=(target_distortion_type == 1))
    target_image = target_calibrator.undistort_image(target_image, 0.0)[0]
    target_keypoints = target_calibrator.undistort_point(target_keypoints)
    K_target, D_target = target_calibrator.camera_matrix_undistorted, target_calibrator.dist_coeffs_undistorted

    # Get visible keypoints from origin camera for pose estimation
    visible_ids = np.where(origin_keypoints[:, 2] == 2)[0]
    image_points = origin_keypoints[visible_ids, :2].astype(np.float32)

    # Get corresponding 3D model points
    model_points = face_mesh_template_3D_MEDIAPIPE_mm_11p
    model_points = model_points[visible_ids, :3]

    # Solve PnP on origin camera to get pose
    rvec_origin, tvec_origin = get_face_landmarks_in_ccs_from_solvePnP(
        model_points, image_points, K_origin, D_origin
    )

    # Draw coordinate axes on origin image
    cv2.drawFrameAxes(origin_image, K_origin, D_origin, rvec_origin, tvec_origin, 50, thickness=1)

    # Project points back to origin image for verification
    proj_points_origin = cv2.projectPoints(
        model_points, rvec_origin, tvec_origin, K_origin, D_origin
    )[0].reshape(-1, 2)
    reprojection_error_origin = compute_reprojection_error(proj_points_origin, image_points)

    # Draw projected points on origin
    for p in proj_points_origin:
        cv2.circle(origin_image, (int(p[0]), int(p[1])), 2, (255, 255, 255), -1)

    # Transform pose from origin camera to target camera using extrinsics
    rvec_target, tvec_target = origin_calibrator.transform_object_pose_between_cameras(
        rvec_obj_in_src_cam=rvec_origin,
        tvec_obj_in_src_cam=tvec_origin,
        src_cam_extrinsic=origin_params["pose"],
        dst_cam_extrinsic=target_params["pose"],
        verbose=False,
    )

    # Draw coordinate axes on target image
    cv2.drawFrameAxes(target_image, K_target, D_target, rvec_target, tvec_target, 50, thickness=1)

    # Get visible keypoints from origin camera for pose estimation
    visible_ids = np.where(target_keypoints[:, 2] == 2)[0]
    target_image_points = target_keypoints[visible_ids, :2].astype(np.float32)

    # Get corresponding 3D model points
    target_model_points = face_mesh_template_3D_MEDIAPIPE_mm_11p[visible_ids, :3]
    
    # Project all model points to target camera
    templates = [face_mesh_template_3D_MEDIAPIPE_mm, face_mesh_template_3D_MEDIAPIPE_mm_11p, target_model_points]
    
    proj_points_target = cv2.projectPoints(
        templates[2], rvec_target, tvec_target, K_target, D_target
    )[0].reshape(-1, 2)
    reprojection_error_target = compute_reprojection_error(proj_points_target, target_image_points)

    # Draw projected points on target
    for p in proj_points_target:
        cv2.circle(target_image, (int(p[0]), int(p[1])), 2, (255, 255, 255), -1)

    pose_info = {
        'origin_cam_name': origin_cam_name,
        'target_cam_name': target_cam_name,
        'visible_keypoints_count': len(visible_ids),
        'origin_image_path': origin_image_path,
        'target_image_path': target_image_path,
        'origin_image': origin_image,
        'target_image': target_image,
        'K_origin': K_origin,
        'D_origin': D_origin,
        'K_target': K_target,
        'D_target': D_target,
        'rvec_origin': rvec_origin,
        'tvec_origin': tvec_origin,
        'rvec_target': rvec_target,
        'tvec_target': tvec_target,
        'origin_keypoints': origin_keypoints,
        'target_keypoints': target_keypoints,
        'proj_points_origin': proj_points_origin,
        'proj_points_target': proj_points_target,
        'reprojection_error_origin': reprojection_error_origin,
        'reprojection_error_target': reprojection_error_target
    }

    return pose_info


def process_all_cameras_from_origin(camera_groups, camera_params, origin_cam_name):
    """
    Process all cameras using the origin camera with most visible keypoints.

    Args:
        camera_groups: Dictionary of camera_name -> sample_group
        camera_params: Camera calibration parameters
        origin_cam_name: Name of origin camera with most visible keypoints

    Returns:
        results: Dictionary with processed results for each target camera
    """
    results = {}
    origin_sample = camera_groups[origin_cam_name]

    if origin_cam_name == "cam_0":
        pose_info = get_pose_for_origin(origin_sample, camera_params, origin_cam_name)
        results[origin_cam_name] = pose_info
        return results
        
    for target_cam_name, target_sample in camera_groups.items():
        if target_cam_name != "cam_0":
            continue

        if target_sample['keypoints'] is None or target_sample['image_path'] is None:
            continue
    
        try:
            pose_info = transform_pose_from_origin_to_target(
                origin_sample,
                target_sample,
                camera_params,
                origin_cam_name,
                target_cam_name
            )
            results[target_cam_name] = pose_info

        except Exception as e:
            print(f"Error processing {target_cam_name}: {str(e)}")
            continue

    return results


def get_cam_0_pose():
    max_points = "9"
    selected_camera = 'cam_0'
    images = 'datasets/DeltaX/multiview/data_v1'
    jsons = 'datasets/DeltaX/multiview/jsons/data_v1'
    cam_params = 'yamls/calibrated_cameras_data_intrinsics_extrinsics_tvs_final.yml'
    filter_ = ''
    
    # Load camera params
    camera_params, tv_params = load_camera_params(cam_params)
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(images, "**", "*.png"), recursive=True)
    )

    # All jsons in the folder
    jsons_paths = sorted(
        glob.glob(os.path.join(jsons, "**", "*.json"), recursive=True)
    )

    # Leave only specific class filter
    image_paths = [name for name in image_paths if filter_ in name]

    # Leave only specific class filter
    jsons_paths = [name for name in jsons_paths if filter_ in name]

    # Match images and keypoints
    scene_groups = match_images_and_keypoints(image_paths, jsons_paths)
    
    # Iterate through the images
    for image_name, camera_groups in tqdm(scene_groups.items()):
        # Find the camera with the most visible keypoints to use as origin
        origin_cam_name, max_visible_count = find_origin_camera_with_most_visible_keypoints(camera_groups)

        # Skip if no origin camera found or not enough visible keypoints
        if origin_cam_name is None or max_visible_count < 5:
            print(f"Skipping {image_name}: Not enough visible keypoints found: {max_visible_count}")
            continue

        print(f"[INFO] Visible keypoints: {max_visible_count:0{len(max_points)}d}/{max_points} | Origin camera: {origin_cam_name}")

        # Process all cameras from origin
        all_cameras_pose_info = process_all_cameras_from_origin(camera_groups, camera_params, origin_cam_name)

        pose_info = all_cameras_pose_info[selected_camera]
        
        return pose_info, origin_cam_name


def display_images():
    selected_camera = 'cam_0'
    images = 'datasets/DeltaX/multiview/data_v1'
    jsons = 'datasets/DeltaX/multiview/jsons/data_v1'
    cam_params = 'yamls/calibrated_cameras_data_intrinsics_extrinsics_tvs_final.yml'
    filter_ = ''
    show_frame = True
    freeze = 0
    
    max_points = '9'
    pad = 200
    size = 600
    size_x, size_y = 400, 250
    point_size = 2
    
    winname_0 = "Selected Camera: 0 - Pose Estimation"
    
    if show_frame:
        for idx, winname in enumerate([winname_0]):
            cv2.namedWindow(winname, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(winname, width=size, height=size)
            cv2.moveWindow(winname, x=(size_x+pad) * idx, y=size_y)
    
    max_points = "9"
    
    # Load camera params
    camera_params, tv_params = load_camera_params(cam_params)
    
    # All images in the folder
    image_paths = sorted(
        glob.glob(os.path.join(images, "**", "*.png"), recursive=True)
    )
    # All jsons in the folder
    jsons_paths = sorted(
        glob.glob(os.path.join(jsons, "**", "*.json"), recursive=True)
    )
    print(jsons_paths)

    # Leave only specific class filter
    image_paths = [name for name in image_paths if filter_ in name]

    # Leave only specific class filter
    jsons_paths = [name for name in jsons_paths if filter_ in name]

    # Match images and keypoints
    scene_groups = match_images_and_keypoints(image_paths, jsons_paths)
    
    # Iterate through the images
    for image_name, camera_groups in tqdm(scene_groups.items()):
        # Find the camera with the most visible keypoints to use as origin
        origin_cam_name, max_visible_count = find_origin_camera_with_most_visible_keypoints(camera_groups)

        # Skip if no origin camera found or not enough visible keypoints
        if origin_cam_name is None or max_visible_count < 5:
            print(f"Skipping {image_name}: Not enough visible keypoints found: {max_visible_count}")
            continue

        print(f"[INFO] Visible keypoints: {max_visible_count:0{len(max_points)}d}/{max_points} | Origin camera: {origin_cam_name}")

        # Process all cameras from origin
        all_cameras_pose_info = process_all_cameras_from_origin(camera_groups, camera_params, origin_cam_name)

        # Display results
        for target_cam_name, pose_info in all_cameras_pose_info.items():
            if "cam_0" not in [origin_cam_name, target_cam_name]:
                continue
            
            if "cam_0" == origin_cam_name:
                print("ORIGNAL")
                selected_image = pose_info['origin_image']
                selected_keypoints = pose_info['origin_keypoints']
                K_selected = pose_info['K_origin']
                D_selected = pose_info['D_origin']
                rvec_selected = pose_info['rvec_origin']
                tvec_selected = pose_info['tvec_origin']
                reprojection_error = pose_info['reprojection_error_origin']
        
            elif "cam_0" == target_cam_name:
                print("TARGET")
                selected_image = pose_info['target_image']
                selected_keypoints = pose_info['target_keypoints']
                K_selected = pose_info['K_target']
                D_selected = pose_info['D_target']
                rvec_selected = pose_info['rvec_target']
                tvec_selected = pose_info['tvec_target']
                reprojection_error = pose_info['reprojection_error_target']
            
            print(f"Reprojection error: {reprojection_error:.0f}px - target")
            
            # Draw coordinate axes on origin image
            cv2.drawFrameAxes(selected_image, K_selected, D_selected, rvec_selected, tvec_selected, 50, thickness=3)
            
            # Draw keypoints on target image
            for x, y, z in selected_keypoints:
                if z == 1:      # invisible
                    cv2.circle(selected_image, (x, y), point_size, (0, 0, 255), -1, cv2.LINE_AA)
                elif z == 2:    # visible
                    cv2.circle(selected_image, (x, y), point_size, (0, 255, 0), -1, cv2.LINE_AA)

            # Get face bounding boxes for cropping
            x0, x1, y0, y1 = get_bbox(selected_image, selected_keypoints, pad)
            selected_image_cropped = selected_image[y0:y1, x0:x1]
            
            # Add text overlay showing camera information
            if show_frame:
                # Add text to images showing camera info
                selected_display = selected_image_cropped.copy()

                cv2.putText(selected_display, f"From {origin_cam_name} | found: ({max_visible_count} pts)", (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
                cv2.imshow(winname_0, selected_display)

                # Get a key
                key = cv2.waitKey(freeze) & 0xFF

                if key == 27:           # Press 'Esc' to quit
                    cv2.destroyAllWindows()
                    exit()
                elif key == ord('q'):   # Move to next image
                    break
                elif key == ord('f'):   # Toggle freeze mode
                    freeze = 0 if freeze else 1

    if show_frame:
        cv2.destroyAllWindows()
