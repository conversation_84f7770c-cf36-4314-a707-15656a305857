from typing import <PERSON><PERSON>, Dict, Any

import cv2
import yaml
import numpy as np


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        Dictionary containing the configuration
    """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def get_camera_matrix(intrinsics_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load camera intrinsic matrix and distortion coefficients from a YAML calibration file.

    Args:
        intrinsics_path: Path to the YAML file containing camera calibration parameters

    Returns:
        Tuple containing:
        - K: 3x3 intrinsic camera matrix
        - D: Distortion coefficients
    """
    config = load_config(intrinsics_path)

    K = np.asarray(config['camera_matrix']).reshape(3, 3)
    D = np.asarray(config['dist_coeff']).reshape(5, 1)
    
    return K, D


def crop_face_from_landmarks(image: np.ndarray,
                             landmarks_2D: np.ndarray,
                             padding: int = 0,
                             output_size: int = 256) -> np.ndarray:
    """
    Crop the face region from the image using 2D landmarks.

    Args:
        image (np.ndarray): Original image (H, W, 3)
        landmarks_2D (np.ndarray): Array of (N, 2) facial landmarks in [u, v] pixel coordinates
        padding (int): Extra pixels to pad around the bounding box
        output_size (int): Size of the output square image

    Returns:
        Cropped face image (np.ndarray)
    """
    h, w = image.shape[:2]
    
    # Step 1: Get tight bounding box from landmarks with padding
    min_x = max(int(np.min(landmarks_2D[:, 0])) - padding, 0)
    max_x = min(int(np.max(landmarks_2D[:, 0])) + padding, w)
    min_y = max(int(np.min(landmarks_2D[:, 1])) - padding, 0)
    max_y = min(int(np.max(landmarks_2D[:, 1])) + padding, h)

    # Step 2: Calculate width and height of the box
    box_w = max_x - min_x
    box_h = max_y - min_y
    max_side = max(box_w, box_h)
    half_side = max_side // 2

    # Step 3: Center the box and adjust to square
    center_x = (min_x + max_x) // 2
    center_y = (min_y + max_y) // 2

    # Step 4: Compute new square box coordinates
    square_min_x = max(center_x - half_side, 0)
    square_max_x = min(center_x + half_side, w)
    square_min_y = max(center_y - half_side, 0)
    square_max_y = min(center_y + half_side, h)

    # Step 5: Crop the square face region
    face_crop = image[square_min_y:square_max_y, square_min_x:square_max_x]
    face_crop_resized = cv2.resize(face_crop, (output_size, output_size), interpolation=cv2.INTER_LINEAR)

    return face_crop_resized


def crop_face_from_landmarks_v2(image: np.ndarray,
                                landmarks_2D: np.ndarray,
                                output_size: int = 96,
                                scale: float = 1.4) -> np.ndarray:
    h, w = image.shape[:2]
    key_ids = [33, 133, 362, 263, 1, 168]  # key central points

    keypoints = landmarks_2D[key_ids]
    min_xy = np.min(keypoints, axis=0)
    max_xy = np.max(keypoints, axis=0)
    center_xy = (min_xy + max_xy) / 2
    box_size = np.max(max_xy - min_xy)

    half_size = int((box_size * scale) / 2)
    center_x, center_y = int(center_xy[0]), int(center_xy[1])

    shift_down = 5
    min_x = max(center_x - half_size, 0)
    max_x = min(center_x + half_size, w)
    min_y = max(center_y - half_size, 0) + shift_down
    max_y = min(center_y + half_size, h) + shift_down

    face_crop = image[min_y:max_y, min_x:max_x]
    face_crop_resized = cv2.resize(face_crop, (output_size, output_size), interpolation=cv2.INTER_LINEAR)
    
    return face_crop_resized


def equalize_hist_rgb(rgb_img: np.ndarray) -> np.ndarray:
    """
    Enhance image contrast by equalizing the histogram of an RGB image.

    This function improves image quality by:
    1. Converting the image to YCrCb color space
    2. Equalizing only the luminance (Y) channel
    3. Converting back to RGB

    Args:
        rgb_img: Input RGB image (not BGR)

    Returns:
        Contrast-enhanced RGB image with equalized histogram
    """
    # Step 1: Convert from RGB to YCrCb color space
    # YCrCb separates luminance (Y) from chrominance (Cr, Cb)
    ycrcb_img = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2YCrCb)

    # Step 2: Equalize only the Y (luminance) channel
    # This enhances contrast while preserving color information
    ycrcb_img[:, :, 0] = cv2.equalizeHist(ycrcb_img[:, :, 0])

    # Step 3: Convert back to RGB color space
    equalized_img = cv2.cvtColor(ycrcb_img, cv2.COLOR_YCrCb2RGB)

    return equalized_img
