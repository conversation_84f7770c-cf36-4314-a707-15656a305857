"""
Gaze Dataset Preprocessing Module

This module handles the preprocessing of raw gaze data to create normalized
face and eye images along with corresponding gaze labels for training.

The preprocessing pipeline:
1. Loads raw images, depth data, and metadata
2. Computes 3D gaze targets
3. Transforms 2D into 3D facial landmarks
4. Computes head pose from 3D landmarks
5. Normalizes face, eye regions, gaze vector
6. Transform 3D gaze vector to 2D gaze angles (pitch and yaw)
7. Saves normalized images and creates a labels CSV
"""
import ast
from tqdm import tqdm
from pathlib import Path

import cv2
import numpy as np
import pandas as pd

from utils.util import (
    get_camera_matrix,
    crop_face_from_landmarks,
    crop_face_from_landmarks_v2,
)
from utils.gaze import (
    normalize,
    is_invalid_vector,
    gaze_vector_to_pitchyaw,
    rotation_alignment_error,
    validate_gaze_vector_recovering,
    pixel_to_camera_point,
    get_face_landmarks_in_ccs_from_depth,
    compute_head_pose_from_landmarks,
    compute_head_pose_solvePnP,
    draw_head_pose_axes,
    get_invSR_vec,
    normalize_single_image,
    normalize_eyes_landmarks,
)
from nets.LandmarksDetector import LandmarksDetectorONNX


def preprocess_gaze_dataset(raw_base: str, normalized_base: str, intrinsics_path) -> None:
    """
    Process raw gaze data to produce normalized face/eye crops and labels CSV.

    Args:
        raw_base: Path to directory containing raw data and data.csv
        normalized_base: Path where normalized data will be saved
        intrinsics_path: Path to YAML file containing camera intrinsics
    """
    # Initialize list to collect entries for the labels CSV
    labels = []
    skipped_count = 0
    processed_count = 0

    # Initialize landmarks detectors
    landmarks_detector = LandmarksDetectorONNX(
        onnx_path="weights/drowsiness_2025_02_14_11.onnx",
        apply_all_optim=True,
        device="cuda",
    ).load_model()
    
    # Read the raw data CSV file
    df = pd.read_csv(raw_base / 'data.csv')

    storage = {"w": [], "h": [], "l": [], "r": []}
    # Process each sample
    for idx, row in tqdm(df.iterrows(), total=len(df), desc=f"Processing {raw_base.name}'s rows"):
        # Extract session, filename
        path = Path(row['file_name'])
        session, filename, basename = path.parent, path.name, path.stem
        
        # sessionQ, filenameQ = "p13", "2025_06_02-11_45_23.jpg"
        # if str(session) != sessionQ or str(filename) != filenameQ:
        #     continue
        
        # Select camera intrinsics based on the month
        month = int(basename.split("-")[0].split("_")[1])
        name = "max" if month >= 7 else "sadat"
        calibration_file = str(intrinsics_path).replace(".yaml", f"_{name}.yaml")
        
        # Load camera calibration parameters
        camera_matrix, dist_coeff = get_camera_matrix(calibration_file)
        
        # Step 1: Load image, 2D landmarks, and depth data
        img_path = raw_base / session / filename
        depth_path = raw_base / session / f"{basename}_depth.npy"
        landmarks_path = raw_base / session / f"{basename}_landmarks.npy"

        if not (img_path.exists() and depth_path.exists() and landmarks_path.exists()):
            print(f"Error: One of the files not found: {raw_base / session / basename}, skipping at {idx}.")
            skipped_count += 1
            continue

        # Loading image, 2D landmarks, and depth data
        img = cv2.imread(str(img_path))
        depth = np.load(str(depth_path))
        landmarks_2D_norm = np.load(str(landmarks_path))  # Normalized landmarks (x,y,z)

        h, w, _ = img.shape                             # Image dimensions
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # Convert to RGB for processing

        # Step 2: Compute 3D gaze target in camera coordinate system
        # Parse screen and camera parameters from CSV
        point_px = ast.literal_eval(row['point_on_screen'])                             # Gaze target in pixels
        monitor_px = ast.literal_eval(row['monitor_px'])                                # Monitor resolution
        monitor_mm = ast.literal_eval(row['monitor_mm'])                                # Monitor dimensions in mm
        rvec_scr2cam = np.asarray(ast.literal_eval(row['rvec']), dtype=np.float64)      # Camera rotation
        tvec_scr2cam_mm = np.asarray(ast.literal_eval(row['tvec']), dtype=np.float64)   # Camera translation

        # Convert a pixel on the screen into a gaze target in the 3D camera coordinate space
        gaze_target = pixel_to_camera_point(
            point_px, monitor_px, monitor_mm, rvec_scr2cam, tvec_scr2cam_mm
        ).flatten()

        # Step 3: Prepare 3D facial landmarks
        # Convert normalized landmarks to pixel coordinates
        landmarks_2D = np.array([[x * w, y * h] for x, y, _ in landmarks_2D_norm])

        # Convert 2D landmarks to 3D using depth information
        landmarks_3D = get_face_landmarks_in_ccs_from_depth(
            landmarks_2D, depth, camera_matrix, filter_depth=True, depth_range=(0.1, 2.0)
        )
        
        # Step 4: Get key points for normalization
        # Extract eye centers and face center from landmarks
        face_center = np.nanmean(landmarks_3D, axis=1).flatten()    # Average of all landmarks
        left_eye_center = landmarks_3D[:, 468].flatten()            # Left eye landmark
        right_eye_center = landmarks_3D[:, 473].flatten()           # Right eye landmark
        eyes_center = (left_eye_center + right_eye_center) / 2      # Element-wise average
        
        # Compute gaze vector from 'eyes center' or 'face_center' to target
        gaze_vector = gaze_target - face_center
        gaze_vector = normalize(gaze_vector)
        
        # Step 5: Compute head pose from 3D landmarks
        rvec_head, R_head = compute_head_pose_from_landmarks(landmarks_3D)
        rvec_head_PnP, R_head_PnP = compute_head_pose_solvePnP(landmarks_2D, camera_matrix, dist_coeff, mediapipe=True)

        # Compare the two rotation matrices
        error, angle_deg = rotation_alignment_error(R_head, R_head_PnP)

        # Skip if the angle difference is too large
        if angle_deg >= 20:
            print(f"Large angle difference: {angle_deg:.3f}, skipping at {idx}: {path}")
            skipped_count += 1
            continue
        
        # Skip if any of the 3D points are invalid vector: near-zero, NaN, or inf
        if (
            is_invalid_vector(gaze_vector)
            or is_invalid_vector(gaze_target)
            or is_invalid_vector(face_center)
            or is_invalid_vector(eyes_center)
            or is_invalid_vector(left_eye_center)
            or is_invalid_vector(right_eye_center)
            or is_invalid_vector(R_head)
            or is_invalid_vector(R_head_PnP)
        ):
            print(f"Invalid vector, skipping at {idx}: {path}")
            skipped_count += 1
            continue
        
        # Save original face image
        # org_face = crop_face_from_landmarks(img_rgb, landmarks_2D, output_size=96, padding=0)
        org_face = crop_face_from_landmarks_v2(img_rgb, landmarks_2D, output_size=96, scale=1.7)
        
        # # Visualize head pose
        head_pose_img = img_rgb.copy()
        head_pose_img_PnP = img_rgb.copy()
        draw_head_pose_axes(
            head_pose_img,
            rvec=rvec_head, tvec=face_center,
            K=camera_matrix, D=dist_coeff,
            axis_length=100, thickness=3,
        )
        draw_head_pose_axes(
            head_pose_img_PnP,
            rvec=rvec_head_PnP, tvec=face_center,
            K=camera_matrix, D=dist_coeff,
            axis_length=100, thickness=3,
        )

        # Step 6: Normalize face and eye images
        # Normalize face image with gaze target
        face_img, gaze_vector, normalized_gaze_vector, normalization_matrices = normalize_single_image(
            img_rgb, R_head_PnP, gaze_vector, face_center, camera_matrix, is_eye=False
        )

        # ================================================================
        # Transform eye landmarks for normalized eye coordinates (experimental)
        face_img_lmk, normalized_left_eye_pixel, normalized_right_eye_pixel = normalize_eyes_landmarks(
            face_img, landmarks_2D, normalization_matrices["W"], draw_points=True
        )

        # Get eye landmarks from normalized face image
        landmark_2D_12, rightEye, leftEye = landmarks_detector.evaluate(face_img.copy(), 96)
        
        # Difference between normalize_eyes_landmarks and detected by AI model in %
        leftEye_error = np.linalg.norm(normalized_left_eye_pixel - leftEye) * 100.0
        rightEye_error = np.linalg.norm(normalized_right_eye_pixel - rightEye) * 100.0
        w, h = np.abs(normalized_left_eye_pixel - normalized_right_eye_pixel) * 100.0
        
        if (leftEye_error > 5) or (rightEye_error > 5) or (32 > w > 50) or (h > 2.5):
            print(f"Invalid eye landmarks mismatching, skipping at {idx}: {path}")
            skipped_count += 1
            continue
        # ================================================================
        
        # Visualize 3D landmarks
        for p in landmarks_2D:
            cv2.circle(head_pose_img, (int(p[0]), int(p[1])), 2, (0, 255, 0), -1)           # green
            cv2.circle(head_pose_img_PnP, (int(p[0]), int(p[1])), 2, (0, 255, 0), -1)       # green
        
        for p in [leftEye, rightEye]:
            cv2.circle(face_img_lmk, (int(p[0]*96), int(p[1]*96)), 2, (255, 255, 0), -1)    # yeallow

        # if (32 > w > 50) or (h > 2.5) or (leftEye_error > 5) or (rightEye_error > 5):
        #     debug = Path(f"./debug")
        #     debug.mkdir(parents=True, exist_ok=True)
        #     debug = str(debug)
        #     print(f"w = {round(w)}%,  h = {round(h)}%")
        #     print(f"Left Eye Error: {round(leftEye_error)}, Right Eye Error: {round(rightEye_error)}")
        #     cv2.imwrite(f"{debug}/HP_{filename}", cv2.cvtColor(head_pose_img, cv2.COLOR_RGB2BGR))
        #     cv2.imwrite(f"{debug}/PnP_{filename}", cv2.cvtColor(head_pose_img_PnP, cv2.COLOR_RGB2BGR))
        #     cv2.imwrite(f"{debug}/lmk_{filename}", cv2.cvtColor(face_img_lmk, cv2.COLOR_RGB2BGR))
        #     skipped_count += 1
        # else:
        #     storage["w"].append(w)
        #     storage["h"].append(h)
        #     storage["l"].append(leftEye_error)
        #     storage["r"].append(rightEye_error)

        # Step 7: Compute pitch and yaw angles from normalized gaze vector
        pitch, yaw = gaze_vector_to_pitchyaw(normalized_gaze_vector)

        # Verify the computed pitch and yaw angles
        validate_gaze_vector_recovering(gaze_vector, normalization_matrices["R"])
        
        # Compute inverse of SR matrix (experimental)
        invSR_vec, denormalized_gaze_vector_3d = get_invSR_vec(
            normalized_gaze_vector, normalization_matrices["R"], normalization_matrices["S"], scaled=True)

        # Step 8: Save images, labels, and metadata
        # Create session folder if it doesn't exist
        session_folder = normalized_base / session
        session_folder_lmk = normalized_base / session / "lmks"
        # session_folder_eyes = normalized_base / session / "eyes"
        session_folder_org = normalized_base / session / "original"
        session_folder_HP = normalized_base / session / "HPvPnP"
        session_folder_Matrices = normalized_base / session / "matrices"
        
        session_folder.mkdir(parents=True, exist_ok=True)
        session_folder_lmk.mkdir(parents=True, exist_ok=True)
        # session_folder_eyes.mkdir(parents=True, exist_ok=True)
        session_folder_org.mkdir(parents=True, exist_ok=True)
        session_folder_HP.mkdir(parents=True, exist_ok=True)
        session_folder_Matrices.mkdir(parents=True, exist_ok=True)

        # Save normalized images
        face_file = session_folder / f"{basename}-face.jpg"
        # left_file = session_folder_eyes / f"{basename}-left-eye.jpg"
        # right_file = session_folder_eyes / f"{basename}-right-eye.jpg"
        org_face_file = session_folder_org / f"{basename}-face.jpg"
        face_eyes_lmk_file = session_folder_lmk / f"{basename}-face-eyes-lmk.jpg"
        head_pose_file = session_folder_HP / f"{basename}-head-pose.jpg"
        head_pose_file_MPII = session_folder_HP / f"{basename}-head-pose-MPII.jpg"
        matrices_file = session_folder_Matrices / f"{basename}.npz"
        
        # Convert back to BGR for OpenCV's imwrite
        params = [cv2.IMWRITE_JPEG_QUALITY, 100]
        cv2.imwrite(str(face_file), cv2.cvtColor(face_img, cv2.COLOR_RGB2BGR), params)
        # cv2.imwrite(str(left_file), cv2.cvtColor(left_img, cv2.COLOR_RGB2BGR), params)
        # cv2.imwrite(str(right_file), cv2.cvtColor(right_img, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(org_face_file), cv2.cvtColor(org_face, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(face_eyes_lmk_file), cv2.cvtColor(face_img_lmk, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(head_pose_file), cv2.cvtColor(head_pose_img, cv2.COLOR_RGB2BGR), params)
        cv2.imwrite(str(head_pose_file_MPII), cv2.cvtColor(head_pose_img_PnP, cv2.COLOR_RGB2BGR), params)
        
        # Save both rotation vector and scaling matrix
        np.savez(matrices_file,
                 R=normalization_matrices["R"],
                 S=normalization_matrices["S"],
                 W=normalization_matrices["W"]
        )

        # Add entry to labels list
        labels.append({
            'face_file_name': f"{session}/{face_file.name}",
            'pitch': pitch,
            'yaw': yaw,
            'gaze_vector_x': gaze_vector[0],
            'gaze_vector_y': gaze_vector[1],
            'gaze_vector_z': gaze_vector[2],
            'normalized_gaze_vector_x': normalized_gaze_vector[0],
            'normalized_gaze_vector_y': normalized_gaze_vector[1],
            'normalized_gaze_vector_z': normalized_gaze_vector[2],
            'normalized_left_eye_center_x': normalized_left_eye_pixel[0],
            'normalized_left_eye_center_y': normalized_left_eye_pixel[1],
            'normalized_right_eye_center_x': normalized_right_eye_pixel[0],
            'normalized_right_eye_center_y': normalized_right_eye_pixel[1],
            'gaze_target_x': gaze_target[0],
            'gaze_target_y': gaze_target[1],
            'gaze_target_z': gaze_target[2],
            'face_center_x': face_center[0],
            'face_center_y': face_center[1],
            'face_center_z': face_center[2],
            'left_eye_center_x': left_eye_center[0],
            'left_eye_center_y': left_eye_center[1],
            'left_eye_center_z': left_eye_center[2],
            'right_eye_center_x': right_eye_center[0],
            'right_eye_center_y': right_eye_center[1],
            'right_eye_center_z': right_eye_center[2],
            'point_on_screen_x': point_px[0],
            'point_on_screen_y': point_px[1],
            'grid_cell': row['grid_cell'],
            'matrices_file': f"{session}/matrices/{basename}.npz",
        })
        processed_count += 1
        # exit()

    # Write labels to CSV file
    if labels:
        labels_df = pd.DataFrame(labels)
        labels_csv_path = normalized_base / 'labels.csv'
        labels_df.to_csv(labels_csv_path, index=False)
        print(f"Saved {len(labels)} entries to {labels_csv_path}")
    else:
        print("No valid samples were processed. No labels.csv file created.")

    # storage["w"] = np.array(storage["w"])
    # storage["h"] = np.array(storage["h"])
    # print(f"Min     w: {storage['w'].min():.2f},    h: {storage['h'].min():.5f}")
    # print(f"Max     w: {storage['w'].max():.2f},    h: {storage['h'].max():.2f}")
    # print(f"Mean    w: {storage['w'].mean():.2f},   h: {storage['h'].mean():.2f}")
    # print(f"Median  w: {np.median(storage['w']):.2f},   h: {np.median(storage['h']):.2f}")

    # storage["l"] = np.array(storage["l"])
    # storage["r"] = np.array(storage["r"])
    # print(f"Min     l: {storage['l'].min():.2f},    r: {storage['r'].min():.5f}")
    # print(f"Max     l: {storage['l'].max():.2f},    r: {storage['r'].max():.2f}")
    # print(f"Mean    l: {storage['l'].mean():.2f},   r: {storage['r'].mean():.2f}")
    # print(f"Median  l: {np.median(storage['l']):.2f},   r: {np.median(storage['r']):.2f}")
    """
    Min     w: 31.98,  Min      h: 0.00
    Max     w: 50.32,  Max      h: 6.19
    Mean    w: 46.70,  Mean     h: 0.78
    Median  w: 47.24,  Median   h: 0.59
    
    Min     l: 0.06,   r: 0.05
    Max     l: 4.87,   r: 4.98
    Mean    l: 1.67,   r: 1.46
    Median  l: 1.55,   r: 1.18
    """

    # Print summary
    print(f"\nPreprocessing Summary:")
    print(f"  Total samples: {len(df)}")
    print(f"  Successfully processed: {processed_count}")
    print(f"  Skipped: {skipped_count}")
    print(f"  Success rate: {processed_count/len(df)*100:.1f}%\n\n")


def run(config):
    # Convert paths to Path objects for easier handling
    dataset_path_raw = Path(config['data_preprocessing']['dataset_path'])
    dataset_path_normalized = Path(config['data_preprocessing']['output_path'])
    intrinsics_path = Path(config['camera']['intrinsics_path'])

    # Print configuration
    print("="*80)
    print(f"Input path: {dataset_path_raw}")
    print(f"Output path: {dataset_path_normalized}")
    print(f"Camera matrix YAML path: {intrinsics_path}")
    print("="*80)

    for _set in ['train', 'test']:
        raw_base = dataset_path_raw / _set
        normalized_base = dataset_path_normalized / _set

        # Run the main preprocessing function
        preprocess_gaze_dataset(raw_base, normalized_base, intrinsics_path)

    print("Preprocessing complete!")
    print(f"The normalized dataset path: {dataset_path_normalized}")


if __name__ == '__main__':
    run()
