import os
from tqdm import tqdm
from pathlib import Path

import cv2
import numpy as np
import pandas as pd


def draw_gaze_arrow_from_vector(img, vector, color=(0, 255, 0), thickness=2, length=50):
    """
    Draw gaze arrow on an image based on pitch and yaw angles.

    Args:
        img: Input image
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians
        color: Arrow color in BGR format (default: red)
        thickness: Arrow thickness
        length: Arrow length in pixels

    Returns:
        Image with drawn arrow
    """
    h, w = img.shape[:2]
    center = (w // 2, h // 2)

    # Get 3D gaze vector (x, y components for 2D projection)
    x, y, _ = vector

    # Project to 2D (image plane), ignoring Z
    dx = x * length
    dy = y * length

    # Calculate end point
    end_point = (int(center[0] + dx), int(center[1] + dy))

    # Draw arrow
    cv2.arrowedLine(img, center, end_point, color, thickness, tipLength=0.3)

    return img


def visualize_gaze(subset_dataset_path_normalized: Path,
                   subset_output_path: Path) -> None:
    """
    Visualize gaze direction with ground truth (green) and prediction (red) arrows.

    Args:
        subset_dataset_path_normalized: Path to dataset directory containing labels.csv and images
        subset_output_path: Path to save visualization results
    """
    # Create output directory
    os.makedirs(subset_output_path / "original", exist_ok=True)
    os.makedirs(subset_output_path / "normalized", exist_ok=True)

    # Load CSV with gaze data
    df = pd.read_csv(subset_dataset_path_normalized / 'labels.csv')

    # Process each sample
    for idx, row in tqdm(df.iterrows(), total=len(df), desc=f"Processing {subset_output_path.name}'s rows"):
        # Get file paths
        filename = Path(row['face_file_name'])
        session, basename = filename.parent, filename.stem
        face_img_org_path = subset_dataset_path_normalized / session / "original" / f"{basename}.jpg"
        face_img_norm_path = subset_dataset_path_normalized / session / f"{basename}.jpg"

        # Load face image
        face_img = cv2.imread(face_img_org_path)
        face_img_norm = cv2.imread(face_img_norm_path)
        if face_img is None or face_img_norm is None:
            print(f"Failed to load image: {face_img_org_path}")
            continue

        # Create a copy of the image for visualization
        img = face_img.copy()
        img_norm = face_img_norm.copy()

        # Get gaze vector
        gaze_vector = np.array([row['gaze_vector_x'],
                                row['gaze_vector_y'],
                                row['gaze_vector_z']], dtype=np.float32)
        gaze_vector_norm = np.array([row['normalized_gaze_vector_x'],
                                     row['normalized_gaze_vector_y'],
                                     row['normalized_gaze_vector_z']], dtype=np.float32)
        
        # Draw ground truth arrow (green)
        img = draw_gaze_arrow_from_vector(img, vector=gaze_vector)
        img_norm = draw_gaze_arrow_from_vector(img_norm, vector=gaze_vector_norm)

        # Save visualization
        output_org_file = os.path.join(subset_output_path / "original", f"{session}-{basename}.jpg")
        output_norn_file = os.path.join(subset_output_path / "normalized", f"{session}-{basename}.jpg")
        cv2.imwrite(output_org_file, img, params=[cv2.IMWRITE_JPEG_QUALITY, 100])
        cv2.imwrite(output_norn_file, img_norm, params=[cv2.IMWRITE_JPEG_QUALITY, 100])


def run(config):
    # Convert paths to Path objects for easier handling
    dataset_path_normalized = Path(config['gaze_directions_visualization']['dataset_path'])
    output_path = Path(config['gaze_directions_visualization']['output_path'])
    
    print("="*80)
    print(f"Input path: {dataset_path_normalized}")
    print(f"Output path: {output_path}")
    print("="*80)

    for _set in ['train', 'test']:
        subset_dataset_path_normalized = dataset_path_normalized / _set
        subset_output_path = output_path / _set

        # Visualize original gaze vector
        visualize_gaze(
            subset_dataset_path_normalized=subset_dataset_path_normalized,
            subset_output_path=subset_output_path,
        )

    print("\nDrawing gaze arrows complete!")
    print(f"All results saved to: {output_path}")


if __name__ == "__main__":
    run()
